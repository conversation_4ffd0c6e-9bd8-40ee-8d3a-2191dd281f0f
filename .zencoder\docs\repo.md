# RK Institute Management System Information

## Summary
The RK Institute Management System is a comprehensive educational institution management platform built with Next.js 14 and TypeScript. It provides functionality for managing students, teachers, courses, fees, assignments, and academic records. The system features a Core Automation Engine that handles routine tasks like monthly billing, fee reminders, and report generation. Version 2.0 is optimized for production with enhanced security and performance features.

## Structure
- **app/**: Next.js application routes and pages (admin, teacher, parent, student portals)
- **components/**: React components organized by functionality (cards, forms, layout, UI)
- **lib/**: Core business logic and services (database, email, fee calculation, security)
- **prisma/**: Database schema and migrations for PostgreSQL
- **scripts/**: Utility scripts for maintenance (backup, health-check, seed data)
- **hooks/**: Custom React hooks organized by domain (financial, student, teacher)
- **public/**: Static assets and resources
- **docs/**: User guides and documentation

## Language & Runtime
**Language**: TypeScript/JavaScript
**Version**: Node.js 18+ (as specified in Dockerfile and package.json engines)
**Build System**: Next.js 14.0.4
**Package Manager**: npm 8+

## Dependencies
**Main Dependencies**:
- Next.js 14.0.4 - React framework with API routes
- Prisma 6.9.0 - ORM for database access
- React 18.2.0 - UI library
- Tailwind CSS - Utility-first CSS framework
- Node-cron 4.1.0 - Scheduling for automation engine
- JWT/bcrypt - Authentication and security
- MCP SDK - Model Context Protocol integration

**Development Dependencies**:
- Playwright 1.53.1 - End-to-end testing
- Jest 29.7.0 - Unit testing
- ESLint/Prettier - Code quality tools
- TypeScript 5.3.3 - Type checking

## Build & Installation
```bash
# Install dependencies
npm install

# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma migrate deploy

# Seed initial data
npm run db:seed

# Build the application
npm run build

# Start the production server
npm start
```

## Docker
**Dockerfile**: Multi-stage build optimized for production
**Image**: Node.js 18 Alpine-based with security hardening
**Configuration**: 
- Multi-stage build (deps → builder → runner)
- Non-root user for security
- Health checks implemented

**Docker Compose**:
- Services: app, PostgreSQL 15 database, Redis cache, Nginx proxy, backup service
- Persistent volumes for data, uploads, and logs
- Health checks for all services
- Network isolation with bridge network

## Core Features
**Automation Engine**:
- Monthly billing generation (5th of month)
- Smart fee reminders (early, due date, overdue)
- Automated report generation (weekly, monthly)
- Operations dashboard for monitoring

**User Management**:
- Role-based access (Admin, Teacher, Parent, Student)
- Family-based user grouping
- Secure authentication with JWT

**Academic Management**:
- Course and service management
- Teacher assignment
- Student enrollment and tracking
- Assignment and homework system

**Financial Management**:
- Fee structure configuration
- Payment tracking
- Discount management
- Financial reporting

## Security Features
- JWT-based authentication with secure storage
- Password hashing with bcrypt (14 rounds)
- Rate limiting and DDoS protection
- Input validation with Zod
- Security headers (CSP, HSTS, XSS protection)
- Database connection security
- Audit logging for all operations