{"aiSyncConfig": {"version": "1.0.0", "project": "RK Institute Management System", "aiSystems": {"augmentCodeAI": {"role": "primary_coordinator", "capabilities": ["codebase_analysis", "file_operations", "architecture_guidance", "quality_assurance", "task_management", "documentation_generation"], "responsibilities": ["Project oversight and coordination", "Code review and quality control", "Architecture decisions", "Production readiness assessment", "Risk management", "Best practices enforcement"]}, "geminiCLI": {"role": "execution_engine", "capabilities": ["code_generation", "terminal_operations", "automated_testing", "debugging_assistance", "documentation_writing", "deployment_automation"], "responsibilities": ["Code implementation", "Terminal command execution", "Automated task execution", "Testing and validation", "Documentation updates", "Deployment operations"]}}, "workflow": {"taskDistribution": {"planning": "augmentCodeAI", "analysis": "augmentCodeAI", "implementation": "geminiCLI", "review": "augmentCodeAI", "testing": "geminiCLI", "validation": "augmentCodeAI", "deployment": "both"}, "handoffProtocol": {"augmentToGemini": ["Provide detailed task specification", "Include context and constraints", "Define success criteria", "Specify quality requirements"], "geminiToAugment": ["Report completion status", "Provide implementation details", "Include test results", "Request review and validation"]}}, "mcpServers": {"filesystem": {"enabled": true, "path": "/rk-institute-management-system"}, "slack": {"enabled": true, "env": "SLACK_BOT_TOKEN"}, "notion": {"enabled": true, "env": "NOTION_API_KEY"}, "github": {"enabled": true, "env": "GITHUB_TOKEN"}, "sentry": {"enabled": true, "env": "SENTRY_DSN"}}, "productionReadiness": {"checkpoints": ["code_quality_validation", "security_audit", "performance_testing", "deployment_verification", "monitoring_setup", "documentation_completion"], "criteria": {"codeQuality": {"testCoverage": ">= 80%", "lintingErrors": "0", "typeScriptErrors": "0", "securityVulnerabilities": "0"}, "performance": {"buildTime": "< 2 minutes", "startupTime": "< 10 seconds", "apiResponseTime": "< 500ms", "memoryUsage": "< 512MB"}, "deployment": {"dockerBuild": "success", "healthChecks": "passing", "environmentVariables": "configured", "databaseMigrations": "applied"}}}}}