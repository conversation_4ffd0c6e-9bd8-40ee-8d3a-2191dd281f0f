# =============================================================================
# RK Institute Management System - Development Environment Configuration
# =============================================================================

# -----------------------------------------------------------------------------
# DATABASE CONFIGURATION (REQUIRED)
# -----------------------------------------------------------------------------
DATABASE_URL="postgresql://postgres:password@localhost:5432/rk_institute_dev"
DATABASE_POOL_MIN="2"
DATABASE_POOL_MAX="10"
DATABASE_TIMEOUT="30000"

# -----------------------------------------------------------------------------
# SECURITY CONFIGURATION (REQUIRED)
# -----------------------------------------------------------------------------
JWT_SECRET="ATLWzXNrolfx6w50hnSZQom0GZxoPyLz1dyRdJijslg="
JWT_EXPIRY="24h"
BCRYPT_ROUNDS="10"

# -----------------------------------------------------------------------------
# APPLICATION CONFIGURATION (REQUIRED)
# -----------------------------------------------------------------------------
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NODE_ENV="development"
PORT="3000"

# -----------------------------------------------------------------------------
# AI INTEGRATION CONFIGURATION
# -----------------------------------------------------------------------------
# Gemini API Key for enhanced AI capabilities (optional but recommended)
GEMINI_API_KEY=""

# MCP Server Configuration
SLACK_BOT_TOKEN=""
NOTION_API_KEY=""
GITHUB_TOKEN=""
SENTRY_DSN=""

# -----------------------------------------------------------------------------
# DEVELOPMENT SETTINGS
# -----------------------------------------------------------------------------
RATE_LIMIT_WINDOW="900000"
RATE_LIMIT_MAX="100"
SECURITY_HEADERS_ENABLED="false"
CORS_ORIGIN="http://localhost:3000"

# -----------------------------------------------------------------------------
# LOGGING & DEBUGGING
# -----------------------------------------------------------------------------
LOG_LEVEL="debug"
ENABLE_AUDIT_LOGS="true"
METRICS_ENABLED="true"

# -----------------------------------------------------------------------------
# DEVELOPMENT TOOLS
# -----------------------------------------------------------------------------
# Enable hot reload and development features
NEXT_PUBLIC_DEV_MODE="true"
HEALTH_CHECK_URL="/api/health"

# -----------------------------------------------------------------------------
# FILE UPLOAD (DEVELOPMENT)
# -----------------------------------------------------------------------------
MAX_FILE_SIZE="10485760"
UPLOAD_PATH="./uploads"

# -----------------------------------------------------------------------------
# OPTIONAL SERVICES (DEVELOPMENT)
# -----------------------------------------------------------------------------
EMAIL_ENABLED="false"
SMS_ENABLED="false"
REDIS_ENABLED="false"
BACKUP_ENABLED="false"
SSL_ENABLED="false"

# -----------------------------------------------------------------------------
# SESSION CONFIGURATION
# -----------------------------------------------------------------------------
SESSION_TIMEOUT="86400000"
MAX_LOGIN_ATTEMPTS="10"
LOCKOUT_TIME="300000"
