{"mcpServers": {"filesystem": {"command": "npx", "args": ["@modelcontextprotocol/server-filesystem", "d:\\P-Projects\\RK-101\\rk-institute-management-system"], "env": {}}, "slack": {"command": "npx", "args": ["@modelcontextprotocol/server-slack"], "env": {"SLACK_BOT_TOKEN": ""}}, "notion": {"command": "npx", "args": ["@ramidecodes/mcp-server-notion"], "env": {"NOTION_API_KEY": ""}}, "github": {"command": "npx", "args": ["@octokit/rest"], "env": {"GITHUB_TOKEN": ""}}}, "prompts": {"rk-institute-context": {"name": "RK Institute Context", "description": "Provides context about the RK Institute Management System", "arguments": [{"name": "task_type", "description": "Type of task (development, testing, deployment, etc.)", "required": true}]}, "code-review": {"name": "Code Review", "description": "Perform code review with RK Institute standards", "arguments": [{"name": "file_path", "description": "Path to file for review", "required": true}]}, "production-check": {"name": "Production Readiness Check", "description": "Check if component/feature is production ready", "arguments": [{"name": "component", "description": "Component or feature to check", "required": true}]}}, "tools": {"health-check": {"name": "System Health Check", "description": "Run comprehensive system health checks", "inputSchema": {"type": "object", "properties": {"check_type": {"type": "string", "enum": ["full", "api", "database", "automation"], "description": "Type of health check to perform"}}}}, "test-runner": {"name": "Test Runner", "description": "Run tests for the RK Institute system", "inputSchema": {"type": "object", "properties": {"test_type": {"type": "string", "enum": ["unit", "integration", "e2e", "all"], "description": "Type of tests to run"}, "component": {"type": "string", "description": "Specific component to test (optional)"}}}}, "deployment-check": {"name": "Deployment Readiness", "description": "Check deployment readiness and perform deployment", "inputSchema": {"type": "object", "properties": {"environment": {"type": "string", "enum": ["staging", "production"], "description": "Target deployment environment"}, "dry_run": {"type": "boolean", "description": "Perform dry run without actual deployment"}}}}}}