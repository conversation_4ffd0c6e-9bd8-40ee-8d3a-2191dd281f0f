# =============================================================================
# RK Institute Management System - Deployment Pipeline
# =============================================================================
# This workflow handles automated deployments to staging and production
# environments based on branch pushes and successful CI checks.

name: 🚀 Deployment Pipeline

on:
  push:
    branches: 
      - main        # Production deployment
      - develop     # Staging deployment
  workflow_run:
    workflows: ["🔄 Continuous Integration"]
    types: [completed]
    branches: [main, develop]

# Ensure only one deployment runs at a time
concurrency:
  group: deploy-${{ github.ref }}
  cancel-in-progress: false

jobs:
  # =============================================================================
  # Pre-deployment Checks
  # =============================================================================
  pre-deployment:
    name: 🔍 Pre-deployment Checks
    runs-on: ubuntu-latest
    if: github.event.workflow_run.conclusion == 'success' || github.event_name == 'push'
    
    outputs:
      environment: ${{ steps.determine-env.outputs.environment }}
      deploy-url: ${{ steps.determine-env.outputs.deploy-url }}
      
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 🎯 Determine deployment environment
        id: determine-env
        run: |
          if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "environment=production" >> $GITHUB_OUTPUT
            echo "deploy-url=https://rk-institute-management-system.vercel.app" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == "refs/heads/develop" ]]; then
            echo "environment=staging" >> $GITHUB_OUTPUT
            echo "deploy-url=https://rk-institute-management-system-staging.vercel.app" >> $GITHUB_OUTPUT
          fi
          
      - name: 📊 Display deployment info
        run: |
          echo "🎯 Target Environment: ${{ steps.determine-env.outputs.environment }}"
          echo "🌐 Deployment URL: ${{ steps.determine-env.outputs.deploy-url }}"
          echo "📝 Commit SHA: ${{ github.sha }}"
          echo "👤 Triggered by: ${{ github.actor }}"

  # =============================================================================
  # Production Deployment
  # =============================================================================
  deploy-production:
    name: 🌟 Production Deployment
    runs-on: ubuntu-latest
    needs: pre-deployment
    if: needs.pre-deployment.outputs.environment == 'production'
    environment: 
      name: production
      url: ${{ needs.pre-deployment.outputs.deploy-url }}
      
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci
        
      - name: 🔧 Generate Prisma client
        run: npx prisma generate
        
      - name: 🏗️ Build application
        run: npm run build
        
      - name: 🚀 Deploy to Vercel (Production)
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
          working-directory: ./
          
      - name: 📊 Deployment success notification
        run: |
          echo "🎉 Production deployment successful!"
          echo "🌐 Live URL: ${{ needs.pre-deployment.outputs.deploy-url }}"
          echo "📝 Commit: ${{ github.sha }}"

  # =============================================================================
  # Staging Deployment
  # =============================================================================
  deploy-staging:
    name: 🧪 Staging Deployment
    runs-on: ubuntu-latest
    needs: pre-deployment
    if: needs.pre-deployment.outputs.environment == 'staging'
    environment: 
      name: staging
      url: ${{ needs.pre-deployment.outputs.deploy-url }}
      
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci
        
      - name: 🔧 Generate Prisma client
        run: npx prisma generate
        
      - name: 🏗️ Build application
        run: npm run build
        
      - name: 🧪 Deploy to Vercel (Staging)
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: ./
          
      - name: 📊 Staging deployment notification
        run: |
          echo "🧪 Staging deployment successful!"
          echo "🌐 Preview URL: ${{ needs.pre-deployment.outputs.deploy-url }}"
          echo "📝 Commit: ${{ github.sha }}"

  # =============================================================================
  # Post-deployment Health Checks
  # =============================================================================
  health-check:
    name: 🏥 Health Check
    runs-on: ubuntu-latest
    needs: [pre-deployment, deploy-production, deploy-staging]
    if: always() && (needs.deploy-production.result == 'success' || needs.deploy-staging.result == 'success')
    
    steps:
      - name: 🏥 Wait for deployment to be ready
        run: sleep 30
        
      - name: 🔍 Health check endpoint
        run: |
          URL="${{ needs.pre-deployment.outputs.deploy-url }}"
          echo "🔍 Checking health of: $URL"
          
          # Basic connectivity check
          if curl -f -s "$URL" > /dev/null; then
            echo "✅ Application is responding"
          else
            echo "❌ Application health check failed"
            exit 1
          fi
          
      - name: 📊 Deployment summary
        run: |
          echo "🎯 Environment: ${{ needs.pre-deployment.outputs.environment }}"
          echo "🌐 URL: ${{ needs.pre-deployment.outputs.deploy-url }}"
          echo "✅ Health check: PASSED"
          echo "🕐 Deployed at: $(date)"
