# =============================================================================
# RK Institute Management System - Release Management
# =============================================================================
# This workflow handles automated versioning, release notes, and changelog
# generation based on conventional commits.

name: 🏷️ Release Management

on:
  push:
    branches: [main]
  workflow_dispatch:
    inputs:
      release_type:
        description: 'Type of release'
        required: true
        default: 'auto'
        type: choice
        options:
          - auto
          - patch
          - minor
          - major

# Ensure only one release runs at a time
concurrency:
  group: release
  cancel-in-progress: false

jobs:
  # =============================================================================
  # Release Preparation
  # =============================================================================
  prepare-release:
    name: 🔍 Prepare Release
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    outputs:
      should-release: ${{ steps.check-changes.outputs.should-release }}
      next-version: ${{ steps.version.outputs.next-version }}
      
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}
          
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci
        
      - name: 🔍 Check for releasable changes
        id: check-changes
        run: |
          # Check if there are any commits since last release
          LAST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "")
          if [ -z "$LAST_TAG" ]; then
            echo "should-release=true" >> $GITHUB_OUTPUT
            echo "No previous releases found, creating initial release"
          else
            COMMITS_SINCE_TAG=$(git rev-list ${LAST_TAG}..HEAD --count)
            if [ "$COMMITS_SINCE_TAG" -gt 0 ]; then
              echo "should-release=true" >> $GITHUB_OUTPUT
              echo "Found $COMMITS_SINCE_TAG commits since last release"
            else
              echo "should-release=false" >> $GITHUB_OUTPUT
              echo "No new commits since last release"
            fi
          fi
          
      - name: 🏷️ Determine next version
        id: version
        if: steps.check-changes.outputs.should-release == 'true'
        run: |
          # Get current version from package.json
          CURRENT_VERSION=$(node -p "require('./package.json').version")
          echo "Current version: $CURRENT_VERSION"
          
          # Determine next version based on commit messages
          if [ "${{ github.event.inputs.release_type }}" != "auto" ] && [ "${{ github.event.inputs.release_type }}" != "" ]; then
            RELEASE_TYPE="${{ github.event.inputs.release_type }}"
          else
            # Auto-detect release type from commit messages
            LAST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "v0.0.0")
            COMMITS=$(git log ${LAST_TAG}..HEAD --pretty=format:"%s")
            
            if echo "$COMMITS" | grep -q "BREAKING CHANGE\|!:"; then
              RELEASE_TYPE="major"
            elif echo "$COMMITS" | grep -q "^feat"; then
              RELEASE_TYPE="minor"
            else
              RELEASE_TYPE="patch"
            fi
          fi
          
          echo "Release type: $RELEASE_TYPE"
          
          # Calculate next version
          IFS='.' read -ra VERSION_PARTS <<< "${CURRENT_VERSION}"
          MAJOR=${VERSION_PARTS[0]}
          MINOR=${VERSION_PARTS[1]}
          PATCH=${VERSION_PARTS[2]}
          
          case $RELEASE_TYPE in
            major)
              MAJOR=$((MAJOR + 1))
              MINOR=0
              PATCH=0
              ;;
            minor)
              MINOR=$((MINOR + 1))
              PATCH=0
              ;;
            patch)
              PATCH=$((PATCH + 1))
              ;;
          esac
          
          NEXT_VERSION="${MAJOR}.${MINOR}.${PATCH}"
          echo "next-version=$NEXT_VERSION" >> $GITHUB_OUTPUT
          echo "Next version: $NEXT_VERSION"

  # =============================================================================
  # Create Release
  # =============================================================================
  create-release:
    name: 🚀 Create Release
    runs-on: ubuntu-latest
    needs: prepare-release
    if: needs.prepare-release.outputs.should-release == 'true'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}
          
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci
        
      - name: 🏷️ Update version in package.json
        run: |
          npm version ${{ needs.prepare-release.outputs.next-version }} --no-git-tag-version
          
      - name: 📝 Generate changelog
        id: changelog
        run: |
          LAST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "")
          if [ -z "$LAST_TAG" ]; then
            COMMITS=$(git log --pretty=format:"- %s (%h)" --reverse)
          else
            COMMITS=$(git log ${LAST_TAG}..HEAD --pretty=format:"- %s (%h)" --reverse)
          fi
          
          # Create changelog content
          CHANGELOG="## What's Changed\n\n$COMMITS\n\n**Full Changelog**: https://github.com/${{ github.repository }}/compare/${LAST_TAG}...v${{ needs.prepare-release.outputs.next-version }}"
          
          # Save changelog to file
          echo -e "$CHANGELOG" > RELEASE_NOTES.md
          
          # Set output for GitHub release
          echo "changelog<<EOF" >> $GITHUB_OUTPUT
          echo -e "$CHANGELOG" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
          
      - name: 🏷️ Create Git tag
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          git add package.json
          git commit -m "chore(release): v${{ needs.prepare-release.outputs.next-version }}"
          git tag -a "v${{ needs.prepare-release.outputs.next-version }}" -m "Release v${{ needs.prepare-release.outputs.next-version }}"
          git push origin main --tags
          
      - name: 🚀 Create GitHub Release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ needs.prepare-release.outputs.next-version }}
          release_name: Release v${{ needs.prepare-release.outputs.next-version }}
          body: ${{ steps.changelog.outputs.changelog }}
          draft: false
          prerelease: false
          
      - name: 📊 Release summary
        run: |
          echo "🎉 Release v${{ needs.prepare-release.outputs.next-version }} created successfully!"
          echo "🏷️ Tag: v${{ needs.prepare-release.outputs.next-version }}"
          echo "📝 Release notes generated"
          echo "🚀 Available at: https://github.com/${{ github.repository }}/releases/tag/v${{ needs.prepare-release.outputs.next-version }}"
