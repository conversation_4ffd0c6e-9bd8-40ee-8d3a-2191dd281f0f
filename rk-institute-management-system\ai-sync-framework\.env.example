# AI Synchronization Framework Configuration

# Gemini CLI Configuration
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_CLI_PATH=gemini
GEMINI_MODEL=gemini-2.5-pro
GEMINI_CONTEXT_WINDOW=1000000
GEMINI_OUTPUT_LIMIT=8192

# Framework Configuration
AI_SYNC_MODE=development
AI_SYNC_LOG_LEVEL=info
AI_SYNC_SESSION_DIR=./sessions
AI_SYNC_CONTEXT_DIR=./context
AI_SYNC_MEMORY_DIR=./memory

# Context Management
CONTEXT_COMPRESSION_ENABLED=true
CONTEXT_TOKEN_BUDGET=10000
REPOSITORY_MAPPING_ENABLED=true
AST_PARSING_ENABLED=true

# Session Management
SESSION_PERSISTENCE_ENABLED=true
SESSION_CHECKPOINT_INTERVAL=300
SESSION_AUTO_SAVE=true
SESSION_MAX_HISTORY=100

# Workflow Configuration
WORKFLOW_AUTO_TRANSITIONS=true
WORKFLOW_QUALITY_GATES=true
WORKFLOW_VALIDATION_ENABLED=true
WORKFLOW_PARALLEL_EXECUTION=false

# Memory Management
MEMORY_PERSISTENCE_ENABLED=true
MEMORY_COMPRESSION_ENABLED=true
MEMORY_AUTO_CLEANUP=true
MEMORY_RETENTION_DAYS=30

# Integration Settings
INTEGRATION_RK_INSTITUTE=true
INTEGRATION_TESTING_ENABLED=true
INTEGRATION_PERFORMANCE_MONITORING=true

# Development Settings
DEV_MODE=true
DEV_VERBOSE_LOGGING=true
DEV_AUTO_RELOAD=true
DEV_MOCK_GEMINI_CLI=false

# Performance Settings
PERFORMANCE_MONITORING=true
PERFORMANCE_METRICS_COLLECTION=true
PERFORMANCE_OPTIMIZATION=true

# Security Settings
SECURITY_VALIDATION=true
SECURITY_SANITIZATION=true
SECURITY_AUDIT_LOGGING=true

# Deployment Settings
DEPLOYMENT_MODE=standalone
DEPLOYMENT_MODULAR_EXTRACTION=true
DEPLOYMENT_AUTO_DOCUMENTATION=true
