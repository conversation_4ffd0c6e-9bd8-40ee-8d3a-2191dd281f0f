{"name": "ai-sync-framework", "version": "1.0.0", "description": "Self-improving AI development framework using Gemini CLI and Augment Code AI synchronization", "main": "index.js", "type": "module", "scripts": {"init": "node scripts/init.js", "ai-sync-dev": "node scripts/ai-sync-dev.js", "build": "node scripts/build.js", "test": "jest", "test:integration": "jest --config jest.integration.config.js", "test:performance": "node tests/performance/run-performance-tests.js", "dev": "nodemon --exec node index.js", "start": "node index.js", "lint": "eslint . --ext .js,.mjs", "lint:fix": "eslint . --ext .js,.mjs --fix", "docs:generate": "node scripts/generate-docs.js", "deploy:prepare": "node scripts/prepare-deployment.js", "extract:component": "node scripts/extract-component.js"}, "keywords": ["ai", "gemini-cli", "development-framework", "context-optimization", "ai-synchronization", "self-improving", "terminal-ai", "pair-programming"], "author": "AI Synchronization Framework Team", "license": "MIT", "dependencies": {"tree-sitter": "^0.21.0", "tree-sitter-javascript": "^0.21.0", "tree-sitter-typescript": "^0.21.0", "tree-sitter-python": "^0.21.0", "fs-extra": "^11.2.0", "yaml": "^2.3.4", "chalk": "^5.3.0", "inquirer": "^9.2.12", "commander": "^11.1.0", "winston": "^3.11.0", "lodash": "^4.17.21", "glob": "^10.3.10", "chokidar": "^3.5.3", "semver": "^7.5.4", "uuid": "^9.0.1", "compression": "^1.7.4", "jsonschema": "^1.4.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "eslint": "^8.56.0", "@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "babel-jest": "^29.7.0", "supertest": "^6.3.3", "tmp": "^0.2.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-org/ai-sync-framework.git"}, "bugs": {"url": "https://github.com/your-org/ai-sync-framework/issues"}, "homepage": "https://github.com/your-org/ai-sync-framework#readme", "jest": {"testEnvironment": "node", "collectCoverage": true, "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"], "testMatch": ["**/tests/**/*.test.js"], "transform": {"^.+\\.js$": "babel-jest"}}, "babel": {"presets": [["@babel/preset-env", {"targets": {"node": "18"}}]]}, "eslintConfig": {"env": {"node": true, "es2022": true, "jest": true}, "extends": ["eslint:recommended"], "parserOptions": {"ecmaVersion": 2022, "sourceType": "module"}, "rules": {"no-console": "warn", "no-unused-vars": "error", "prefer-const": "error", "no-var": "error"}}}