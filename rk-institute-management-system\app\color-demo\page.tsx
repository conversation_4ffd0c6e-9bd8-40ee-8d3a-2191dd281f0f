/**
 * Professional Color & Visual System Demo
 * 
 * Showcases the enhanced professional color palette, sophisticated gradients,
 * and advanced visual components for the RK Institute Management System.
 */

'use client';

import React, { useState } from 'react';
import {
  Container,
  Section,
  PageTitle,
  SectionTitle,
  BodyText,
  ModernCard,
  ProfessionalCard,
  StatusBadge,
  ProfessionalAlert,
  MobileButton
} from '@/components/ui';

export default function ColorDemo() {
  const [showAlert, setShowAlert] = useState(true);

  const gradients = [
    { name: 'Brand', key: 'brand', description: 'Primary brand gradient' },
    { name: 'Ocean', key: 'ocean', description: 'Deep blue ocean waves' },
    { name: 'Sunset', key: 'sunset', description: 'Warm sunset colors' },
    { name: 'Forest', key: 'forest', description: 'Fresh forest breeze' },
    { name: 'Royal', key: 'royal', description: 'Royal purple elegance' },
    { name: 'Cosmic', key: 'cosmic', description: 'Cosmic space vibes' },
  ];

  const statusTypes = [
    { type: 'success', label: 'Success', description: 'Completed successfully' },
    { type: 'warning', label: 'Warning', description: 'Requires attention' },
    { type: 'error', label: 'Error', description: 'Action failed' },
    { type: 'info', label: 'Info', description: 'Additional information' },
    { type: 'neutral', label: 'Neutral', description: 'Standard status' },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Container size="lg" className="py-6">
        {/* Header */}
        <Section className="mb-8">
          <PageTitle>Professional Color System</PageTitle>
          <BodyText>
            Experience our sophisticated color palette, advanced gradients, and 
            professional visual components designed for modern interfaces.
          </BodyText>
        </Section>

        {/* Professional Alerts Demo */}
        <Section className="mb-8">
          <SectionTitle>Professional Alerts</SectionTitle>
          <div className="space-y-4">
            <ProfessionalAlert 
              type="info" 
              title="Information Alert"
              dismissible
              onDismiss={() => setShowAlert(false)}
            >
              This is an informational alert with a dismissible action. Click the X to dismiss.
            </ProfessionalAlert>
            
            <ProfessionalAlert type="success" title="Success Alert">
              Your changes have been saved successfully. All data is now synchronized.
            </ProfessionalAlert>
            
            <ProfessionalAlert type="warning" title="Warning Alert">
              Please review your settings. Some configurations may need attention.
            </ProfessionalAlert>
            
            <ProfessionalAlert type="error" title="Error Alert">
              Unable to process your request. Please check your connection and try again.
            </ProfessionalAlert>
          </div>
        </Section>

        {/* Status Badges Demo */}
        <Section className="mb-8">
          <SectionTitle>Status Badges</SectionTitle>
          <ModernCard variant="elevated" size="md">
            <div className="space-y-6">
              {statusTypes.map((status) => (
                <div key={status.type} className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-gray-900">{status.label}</h4>
                    <p className="text-sm text-gray-600">{status.description}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <StatusBadge status={status.type as any} size="sm">
                      Small
                    </StatusBadge>
                    <StatusBadge status={status.type as any} size="md">
                      Medium
                    </StatusBadge>
                    <StatusBadge status={status.type as any} size="lg">
                      Large
                    </StatusBadge>
                  </div>
                </div>
              ))}
            </div>
          </ModernCard>
        </Section>

        {/* Professional Card Variants */}
        <Section className="mb-8">
          <SectionTitle>Professional Card Variants</SectionTitle>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <ProfessionalCard variant="standard" className="p-6">
              <h3 className="text-lg font-semibold mb-2">Standard Card</h3>
              <p className="text-gray-600">Clean and professional design with subtle shadows.</p>
              <div className="mt-4">
                <StatusBadge status="info" size="sm">Standard</StatusBadge>
              </div>
            </ProfessionalCard>

            <ProfessionalCard variant="elevated" className="p-6">
              <h3 className="text-lg font-semibold mb-2">Elevated Card</h3>
              <p className="text-gray-600">Enhanced elevation with stronger shadows and hover effects.</p>
              <div className="mt-4">
                <StatusBadge status="success" size="sm">Elevated</StatusBadge>
              </div>
            </ProfessionalCard>

            <ProfessionalCard variant="glass" className="p-6">
              <h3 className="text-lg font-semibold mb-2">Glass Card</h3>
              <p className="text-gray-600">Modern glassmorphism effect with backdrop blur.</p>
              <div className="mt-4">
                <StatusBadge status="neutral" size="sm">Glass</StatusBadge>
              </div>
            </ProfessionalCard>
          </div>
        </Section>

        {/* Gradient Showcase */}
        <Section className="mb-8">
          <SectionTitle>Sophisticated Gradients</SectionTitle>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {gradients.map((gradient) => (
              <ProfessionalCard 
                key={gradient.key}
                variant="gradient" 
                gradient={gradient.key as any}
                className="p-6 text-center"
              >
                <h3 className="text-xl font-bold mb-2">{gradient.name}</h3>
                <p className="text-white/90 mb-4">{gradient.description}</p>
                <div className="inline-flex items-center px-3 py-1 bg-white/20 rounded-full text-sm font-medium">
                  {gradient.key}-gradient
                </div>
              </ProfessionalCard>
            ))}
          </div>
        </Section>

        {/* Professional Buttons */}
        <Section className="mb-8">
          <SectionTitle>Professional Button Styles</SectionTitle>
          <ModernCard variant="elevated" size="md">
            <div className="space-y-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Primary Actions</h4>
                <div className="flex flex-wrap gap-3">
                  <button className="btn-professional-primary px-6 py-3 rounded-lg font-medium">
                    Primary Button
                  </button>
                  <button className="btn-professional-secondary px-6 py-3 rounded-lg font-medium">
                    Secondary Button
                  </button>
                  <button className="glass-button px-6 py-3 rounded-lg font-medium text-gray-700">
                    Glass Button
                  </button>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-3">Status Actions</h4>
                <div className="flex flex-wrap gap-3">
                  <button className="bg-success-gradient px-6 py-3 rounded-lg font-medium text-white">
                    Success Action
                  </button>
                  <button className="bg-warning-gradient px-6 py-3 rounded-lg font-medium text-white">
                    Warning Action
                  </button>
                  <button className="bg-error-gradient px-6 py-3 rounded-lg font-medium text-white">
                    Error Action
                  </button>
                </div>
              </div>
            </div>
          </ModernCard>
        </Section>

        {/* Professional Shadows */}
        <Section className="mb-8">
          <SectionTitle>Professional Shadow System</SectionTitle>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {['xs', 'sm', 'md', 'lg', 'xl', '2xl', '3xl'].map((size, index) => (
              <div
                key={size}
                className={`bg-white p-4 rounded-lg shadow-professional-${size} text-center`}
              >
                <div className="w-8 h-8 bg-primary-100 rounded-lg mx-auto mb-2"></div>
                <p className="text-sm font-medium text-gray-900">Shadow {size.toUpperCase()}</p>
                <p className="text-xs text-gray-500">Level {index + 1}</p>
              </div>
            ))}
          </div>
        </Section>

        {/* Color Palette */}
        <Section className="mb-8">
          <SectionTitle>Professional Color Palette</SectionTitle>
          <div className="space-y-6">
            {/* Brand Colors */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Brand Colors</h4>
              <div className="flex flex-wrap gap-2">
                {[50, 100, 200, 300, 400, 500, 600, 700, 800, 900].map((shade) => (
                  <div key={shade} className="text-center">
                    <div 
                      className={`w-12 h-12 rounded-lg mb-1 bg-primary-${shade}`}
                      title={`primary-${shade}`}
                    ></div>
                    <span className="text-xs text-gray-600">{shade}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Status Colors */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Success Colors</h4>
                <div className="flex flex-wrap gap-1">
                  {[100, 300, 500, 700].map((shade) => (
                    <div key={shade} className="text-center">
                      <div 
                        className={`w-8 h-8 rounded bg-success-${shade}`}
                        title={`success-${shade}`}
                      ></div>
                      <span className="text-xs text-gray-600">{shade}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-3">Warning Colors</h4>
                <div className="flex flex-wrap gap-1">
                  {[100, 300, 500, 700].map((shade) => (
                    <div key={shade} className="text-center">
                      <div 
                        className={`w-8 h-8 rounded bg-warning-${shade}`}
                        title={`warning-${shade}`}
                      ></div>
                      <span className="text-xs text-gray-600">{shade}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-3">Error Colors</h4>
                <div className="flex flex-wrap gap-1">
                  {[100, 300, 500, 700].map((shade) => (
                    <div key={shade} className="text-center">
                      <div 
                        className={`w-8 h-8 rounded bg-error-${shade}`}
                        title={`error-${shade}`}
                      ></div>
                      <span className="text-xs text-gray-600">{shade}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </Section>

        {/* Usage Examples */}
        <Section className="mb-8">
          <SectionTitle>Real-World Usage Examples</SectionTitle>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Student Card Example */}
            <ProfessionalCard variant="elevated" className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">John Doe</h3>
                  <p className="text-gray-600">Computer Science • Grade A</p>
                </div>
                <StatusBadge status="success" size="sm">Active</StatusBadge>
              </div>
              <div className="space-y-2 text-sm text-gray-600">
                <p>Student ID: CS2024001</p>
                <p>Enrollment Date: Jan 15, 2024</p>
                <p>Contact: <EMAIL></p>
              </div>
              <div className="mt-4 flex space-x-2">
                <button className="btn-professional-primary px-4 py-2 rounded-md text-sm">
                  View Details
                </button>
                <button className="btn-professional-secondary px-4 py-2 rounded-md text-sm">
                  Edit Profile
                </button>
              </div>
            </ProfessionalCard>

            {/* Fee Management Example */}
            <ProfessionalCard variant="elevated" className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Fee Payment</h3>
                  <p className="text-gray-600">Semester Fee - Spring 2024</p>
                </div>
                <StatusBadge status="warning" size="sm">Pending</StatusBadge>
              </div>
              <div className="space-y-2 text-sm text-gray-600">
                <p>Amount: $2,500.00</p>
                <p>Due Date: March 15, 2024</p>
                <p>Payment Method: Bank Transfer</p>
              </div>
              <div className="mt-4">
                <ProfessionalAlert type="warning">
                  Payment is due in 5 days. Please complete the payment to avoid late fees.
                </ProfessionalAlert>
              </div>
            </ProfessionalCard>
          </div>
        </Section>
      </Container>
    </div>
  );
}
