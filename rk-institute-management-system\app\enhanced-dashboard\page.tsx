/**
 * Enhanced Dashboard Demo
 * 
 * Showcases the complete UI/UX enhancement system applied to a real dashboard
 * with improved typography, modern cards, mobile optimization, and professional colors.
 */

'use client';

import React, { useState } from 'react';
import {
  Container,
  Section,
  PageTitle,
  SectionTitle,
  BodyText,
  HeadingText,
  ModernCard,
  ModernStatsCard,
  ProfessionalCard,
  StatusBadge,
  ProfessionalAlert,
  MobileButton,
  MobileBottomNav,
  MobileFAB,
  MobileCardStack,
  MobileListItem
} from '@/components/ui';

// Demo icons
const DashboardIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
  </svg>
);

const StudentsIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
  </svg>
);

const CoursesIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
  </svg>
);

const FeesIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
  </svg>
);

const PlusIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
  </svg>
);

export default function EnhancedDashboard() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [showAlert, setShowAlert] = useState(true);

  // Mock dashboard data
  const dashboardStats = {
    totalStudents: 1247,
    activeStudents: 1189,
    totalCourses: 24,
    activeCourses: 18,
    monthlyRevenue: 485000,
    outstandingFees: 125000,
    newEnrollments: 45,
    completionRate: 94.2
  };

  const recentActivities = [
    {
      id: 1,
      title: 'New Student Enrollment',
      subtitle: 'Sarah Johnson enrolled in Computer Science',
      time: '2 minutes ago',
      status: 'success' as const
    },
    {
      id: 2,
      title: 'Fee Payment Received',
      subtitle: 'John Doe - ₹15,000 payment processed',
      time: '15 minutes ago',
      status: 'success' as const
    },
    {
      id: 3,
      title: 'Course Capacity Alert',
      subtitle: 'Mathematics course is 90% full',
      time: '1 hour ago',
      status: 'warning' as const
    },
    {
      id: 4,
      title: 'Assignment Submission',
      subtitle: '25 students submitted Physics assignment',
      time: '2 hours ago',
      status: 'info' as const
    }
  ];

  const navItems = [
    { label: 'Dashboard', icon: <DashboardIcon />, active: activeTab === 'dashboard' },
    { label: 'Students', icon: <StudentsIcon />, active: activeTab === 'students' },
    { label: 'Courses', icon: <CoursesIcon />, active: activeTab === 'courses' },
    { label: 'Fees', icon: <FeesIcon />, active: activeTab === 'fees' },
  ];

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      <Container size="lg" className="py-6">
        {/* Enhanced Header */}
        <Section className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <PageTitle>RK Institute Dashboard</PageTitle>
              <BodyText className="mt-2">
                Welcome back! Here's what's happening at your institute today.
              </BodyText>
            </div>
            <div className="mt-4 sm:mt-0">
              <StatusBadge status="success" size="md">
                System Online
              </StatusBadge>
            </div>
          </div>
        </Section>

        {/* System Alert */}
        {showAlert && (
          <Section className="mb-8">
            <ProfessionalAlert 
              type="info" 
              title="System Update"
              dismissible
              onDismiss={() => setShowAlert(false)}
            >
              New features have been deployed! Check out the enhanced mobile experience and improved analytics.
            </ProfessionalAlert>
          </Section>
        )}

        {/* Enhanced Stats Overview */}
        <Section className="mb-8">
          <SectionTitle>Key Metrics</SectionTitle>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <ModernStatsCard
              title="Total Students"
              value={dashboardStats.totalStudents.toLocaleString()}
              change={{
                value: "****%",
                type: "increase"
              }}
              icon={<StudentsIcon />}
              color="blue"
            />
            <ModernStatsCard
              title="Active Courses"
              value={dashboardStats.activeCourses}
              change={{
                value: "+2",
                type: "increase"
              }}
              icon={<CoursesIcon />}
              color="green"
            />
            <ModernStatsCard
              title="Monthly Revenue"
              value={`₹${(dashboardStats.monthlyRevenue / 1000).toFixed(0)}K`}
              change={{
                value: "+12.5%",
                type: "increase"
              }}
              icon={<FeesIcon />}
              color="purple"
            />
            <ModernStatsCard
              title="Completion Rate"
              value={`${dashboardStats.completionRate}%`}
              change={{
                value: "+2.1%",
                type: "increase"
              }}
              icon={<DashboardIcon />}
              color="orange"
            />
          </div>
        </Section>

        {/* Professional Cards Showcase */}
        <Section className="mb-8">
          <SectionTitle>Quick Actions</SectionTitle>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <ProfessionalCard variant="gradient" gradient="brand" className="p-6 text-center">
              <StudentsIcon />
              <HeadingText level={3} className="text-white mt-4 mb-2">
                Student Management
              </HeadingText>
              <BodyText className="text-white/90 mb-4">
                Manage enrollments, profiles, and academic records
              </BodyText>
              <MobileButton variant="ghost" size="md" className="bg-white/20 text-white border-white/30">
                View Students
              </MobileButton>
            </ProfessionalCard>

            <ProfessionalCard variant="gradient" gradient="forest" className="p-6 text-center">
              <CoursesIcon />
              <HeadingText level={3} className="text-white mt-4 mb-2">
                Course Management
              </HeadingText>
              <BodyText className="text-white/90 mb-4">
                Create and manage courses, schedules, and curriculum
              </BodyText>
              <MobileButton variant="ghost" size="md" className="bg-white/20 text-white border-white/30">
                View Courses
              </MobileButton>
            </ProfessionalCard>

            <ProfessionalCard variant="gradient" gradient="sunset" className="p-6 text-center">
              <FeesIcon />
              <HeadingText level={3} className="text-white mt-4 mb-2">
                Fee Management
              </HeadingText>
              <BodyText className="text-white/90 mb-4">
                Handle payments, billing, and financial reporting
              </BodyText>
              <MobileButton variant="ghost" size="md" className="bg-white/20 text-white border-white/30">
                View Fees
              </MobileButton>
            </ProfessionalCard>
          </div>
        </Section>

        {/* Recent Activities */}
        <Section className="mb-8">
          <SectionTitle>Recent Activities</SectionTitle>
          <MobileCardStack spacing="normal">
            {recentActivities.map((activity) => (
              <MobileListItem
                key={activity.id}
                title={activity.title}
                subtitle={`${activity.subtitle} • ${activity.time}`}
                rightContent={
                  <StatusBadge status={activity.status} size="sm">
                    {activity.status}
                  </StatusBadge>
                }
              />
            ))}
          </MobileCardStack>
        </Section>

        {/* Performance Insights */}
        <Section className="mb-8">
          <SectionTitle>Performance Insights</SectionTitle>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ProfessionalCard variant="elevated" className="p-6">
              <HeadingText level={3} className="mb-4">
                Enrollment Trends
              </HeadingText>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">This Month</span>
                  <div className="flex items-center">
                    <div className="w-32 bg-gray-200 rounded-full h-2 mr-3">
                      <div className="bg-brand-gradient h-2 rounded-full" style={{ width: '75%' }}></div>
                    </div>
                    <span className="font-semibold">75%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Last Month</span>
                  <div className="flex items-center">
                    <div className="w-32 bg-gray-200 rounded-full h-2 mr-3">
                      <div className="bg-success-gradient h-2 rounded-full" style={{ width: '68%' }}></div>
                    </div>
                    <span className="font-semibold">68%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Target</span>
                  <div className="flex items-center">
                    <div className="w-32 bg-gray-200 rounded-full h-2 mr-3">
                      <div className="bg-warning-gradient h-2 rounded-full" style={{ width: '85%' }}></div>
                    </div>
                    <span className="font-semibold">85%</span>
                  </div>
                </div>
              </div>
            </ProfessionalCard>

            <ProfessionalCard variant="elevated" className="p-6">
              <HeadingText level={3} className="mb-4">
                Financial Overview
              </HeadingText>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Total Revenue</span>
                  <span className="font-bold text-green-600">₹4,85,000</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Outstanding</span>
                  <span className="font-bold text-orange-600">₹1,25,000</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Collection Rate</span>
                  <span className="font-bold text-blue-600">79.4%</span>
                </div>
                <div className="pt-2 border-t">
                  <div className="flex justify-between items-center">
                    <span className="font-semibold text-gray-900">Net Income</span>
                    <span className="font-bold text-gray-900">₹3,60,000</span>
                  </div>
                </div>
              </div>
            </ProfessionalCard>
          </div>
        </Section>
      </Container>

      {/* Mobile Bottom Navigation */}
      <MobileBottomNav 
        items={navItems.map(item => ({
          ...item,
          onClick: () => setActiveTab(item.label.toLowerCase())
        }))}
      />

      {/* Floating Action Button */}
      <MobileFAB
        icon={<PlusIcon />}
        onClick={() => alert('Add new item')}
        position="bottom-right"
      />
    </div>
  );
}
