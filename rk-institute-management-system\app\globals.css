@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Enhanced Typography System */
@import '../styles/typography.css';

/* Import Mobile-First Responsive System */
@import '../styles/responsive.css';

/* Import Professional Color & Visual System */
@import '../styles/professional-colors.css';

@layer base {
  html {
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }

  body {
    @apply antialiased font-body text-gray-700 bg-gray-50;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }

  /* Enhanced heading styles */
  h1, h2, h3, h4, h5, h6 {
    @apply font-heading text-gray-900;
  }

  /* Improved link styles */
  a {
    @apply transition-colors duration-200;
  }

  /* Better focus styles for accessibility */
  *:focus {
    @apply outline-none;
  }

  *:focus-visible {
    @apply ring-2 ring-primary-500 ring-offset-2 rounded;
  }
}

@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-0.5 focus:outline-none focus:ring-4 focus:ring-blue-500/25;
  }

  .btn-secondary {
    @apply bg-white hover:bg-gray-50 text-gray-700 font-medium py-3 px-6 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-0.5 focus:outline-none focus:ring-4 focus:ring-gray-500/25;
  }

  .btn-danger {
    @apply bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-medium py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-0.5 focus:outline-none focus:ring-4 focus:ring-red-500/25;
  }

  .btn-success {
    @apply bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-0.5 focus:outline-none focus:ring-4 focus:ring-green-500/25;
  }

  .input-field {
    @apply w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-blue-500/25 focus:border-blue-500 transition-all duration-300 bg-white/50 backdrop-blur-sm;
  }

  .card {
    @apply bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-100 p-4 sm:p-8 transition-all duration-300 hover:shadow-2xl;
  }

  .card-compact {
    @apply bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-gray-100 p-3 sm:p-6 transition-all duration-300 hover:shadow-xl;
  }

  .table-container {
    @apply overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-100;
  }

  .table-header {
    @apply bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider border-b border-gray-200;
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-b border-gray-50;
  }

  .gradient-bg {
    @apply bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50;
  }

  .glass-effect {
    @apply bg-white/20 backdrop-blur-md border border-white/30;
  }

  .animate-fade-in {
    animation: fadeIn 0.6s ease-out forwards;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out forwards;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
