/**
 * Mobile-First Responsive Demo Page
 * 
 * Showcases the mobile-optimized components and responsive design
 * enhancements for the RK Institute Management System.
 */

'use client';

import React, { useState } from 'react';
import {
  Container,
  Section,
  PageTitle,
  SectionTitle,
  BodyText,
  MobileButton,
  MobileInput,
  MobileBottomNav,
  MobileCardStack,
  MobileListItem,
  MobileActionSheet,
  MobileFAB,
  ModernCard
} from '@/components/ui';

// Demo icons
const HomeIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
  </svg>
);

const UserIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
  </svg>
);

const BookIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
  </svg>
);

const SettingsIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
  </svg>
);

const PlusIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
  </svg>
);

const EditIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
  </svg>
);

const DeleteIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
  </svg>
);

export default function MobileDemo() {
  const [activeTab, setActiveTab] = useState('home');
  const [showActionSheet, setShowActionSheet] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: ''
  });

  const navItems = [
    { label: 'Home', icon: <HomeIcon />, href: '#', active: activeTab === 'home' },
    { label: 'Students', icon: <UserIcon />, href: '#', active: activeTab === 'students' },
    { label: 'Courses', icon: <BookIcon />, href: '#', active: activeTab === 'courses' },
    { label: 'Settings', icon: <SettingsIcon />, href: '#', active: activeTab === 'settings' },
  ];

  const actionSheetActions = [
    {
      label: 'Edit Student',
      icon: <EditIcon />,
      onClick: () => alert('Edit student action')
    },
    {
      label: 'View Details',
      icon: <UserIcon />,
      onClick: () => alert('View details action')
    },
    {
      label: 'Delete Student',
      icon: <DeleteIcon />,
      onClick: () => alert('Delete student action'),
      destructive: true
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      <Container size="lg" className="py-6">
        {/* Header */}
        <Section className="mb-8">
          <PageTitle>Mobile-First Design</PageTitle>
          <BodyText>
            Experience our enhanced mobile-optimized components designed for 
            touch interactions and responsive layouts.
          </BodyText>
        </Section>

        {/* Mobile Buttons Demo */}
        <Section className="mb-8">
          <SectionTitle>Mobile-Optimized Buttons</SectionTitle>
          <ModernCard variant="elevated" size="md">
            <div className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <MobileButton variant="primary" size="md" fullWidth>
                  Primary Button
                </MobileButton>
                <MobileButton variant="secondary" size="md" fullWidth>
                  Secondary Button
                </MobileButton>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <MobileButton 
                  variant="outline" 
                  size="lg" 
                  fullWidth
                  icon={<UserIcon />}
                  iconPosition="left"
                >
                  With Icon
                </MobileButton>
                <MobileButton 
                  variant="ghost" 
                  size="lg" 
                  fullWidth
                  loading
                >
                  Loading State
                </MobileButton>
              </div>
            </div>
          </ModernCard>
        </Section>

        {/* Mobile Form Demo */}
        <Section className="mb-8">
          <SectionTitle>Mobile-Optimized Forms</SectionTitle>
          <ModernCard variant="elevated" size="md">
            <div className="space-y-4">
              <MobileInput
                label="Full Name"
                placeholder="Enter your full name"
                value={formData.name}
                onChange={(value) => setFormData(prev => ({ ...prev, name: value }))}
                required
              />
              <MobileInput
                label="Email Address"
                type="email"
                placeholder="Enter your email"
                value={formData.email}
                onChange={(value) => setFormData(prev => ({ ...prev, email: value }))}
                helper="We'll never share your email with anyone else."
              />
              <MobileInput
                label="Phone Number"
                type="tel"
                placeholder="Enter your phone number"
                value={formData.phone}
                onChange={(value) => setFormData(prev => ({ ...prev, phone: value }))}
              />
              <MobileButton variant="primary" size="lg" fullWidth>
                Submit Form
              </MobileButton>
            </div>
          </ModernCard>
        </Section>

        {/* Mobile List Demo */}
        <Section className="mb-8">
          <SectionTitle>Mobile-Optimized Lists</SectionTitle>
          <MobileCardStack spacing="normal">
            <MobileListItem
              title="John Doe"
              subtitle="Computer Science • Grade A"
              icon={<UserIcon />}
              rightContent={
                <span className="text-sm text-green-600 font-medium">Active</span>
              }
              onClick={() => setShowActionSheet(true)}
            />
            <MobileListItem
              title="Jane Smith"
              subtitle="Mathematics • Grade B+"
              icon={<UserIcon />}
              rightContent={
                <span className="text-sm text-green-600 font-medium">Active</span>
              }
              onClick={() => setShowActionSheet(true)}
            />
            <MobileListItem
              title="Mike Johnson"
              subtitle="Physics • Grade A-"
              icon={<UserIcon />}
              rightContent={
                <span className="text-sm text-yellow-600 font-medium">Pending</span>
              }
              onClick={() => setShowActionSheet(true)}
            />
            <MobileListItem
              title="Sarah Wilson"
              subtitle="Chemistry • Grade B"
              icon={<UserIcon />}
              rightContent={
                <span className="text-sm text-green-600 font-medium">Active</span>
              }
              onClick={() => setShowActionSheet(true)}
            />
          </MobileCardStack>
        </Section>

        {/* Responsive Grid Demo */}
        <Section className="mb-8">
          <SectionTitle>Responsive Grid System</SectionTitle>
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4">
            {[1, 2, 3, 4, 5, 6, 7, 8].map((item) => (
              <ModernCard key={item} variant="elevated" size="sm" className="text-center">
                <div className="w-12 h-12 bg-primary-100 rounded-lg mx-auto mb-2 flex items-center justify-center">
                  <span className="text-primary-600 font-semibold">{item}</span>
                </div>
                <p className="text-sm font-medium text-gray-900">Item {item}</p>
                <p className="text-xs text-gray-500">Responsive</p>
              </ModernCard>
            ))}
          </div>
        </Section>

        {/* Touch Targets Demo */}
        <Section className="mb-8">
          <SectionTitle>Touch-Friendly Targets</SectionTitle>
          <ModernCard variant="elevated" size="md">
            <BodyText className="mb-4">
              All interactive elements meet the minimum 44px touch target size 
              for optimal mobile usability.
            </BodyText>
            <div className="flex flex-wrap gap-3">
              <button className="touch-target bg-primary-100 text-primary-700 rounded-lg px-4 py-2 font-medium">
                44px Min
              </button>
              <button className="touch-target-large bg-green-100 text-green-700 rounded-lg px-4 py-2 font-medium">
                48px Large
              </button>
              <button className="touch-target bg-purple-100 text-purple-700 rounded-lg px-4 py-2 font-medium">
                Touch Safe
              </button>
            </div>
          </ModernCard>
        </Section>

        {/* Device Preview */}
        <Section className="mb-8">
          <SectionTitle>Device Responsiveness</SectionTitle>
          <ModernCard variant="glass" size="md">
            <div className="text-center space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
                <div className="p-4 bg-white/50 rounded-lg">
                  <div className="w-8 h-12 bg-gray-400 rounded mx-auto mb-2"></div>
                  <p className="font-medium">Mobile</p>
                  <p className="text-gray-600">320px - 639px</p>
                </div>
                <div className="p-4 bg-white/50 rounded-lg">
                  <div className="w-12 h-8 bg-gray-400 rounded mx-auto mb-2"></div>
                  <p className="font-medium">Tablet</p>
                  <p className="text-gray-600">640px - 1023px</p>
                </div>
                <div className="p-4 bg-white/50 rounded-lg">
                  <div className="w-16 h-10 bg-gray-400 rounded mx-auto mb-2"></div>
                  <p className="font-medium">Desktop</p>
                  <p className="text-gray-600">1024px+</p>
                </div>
              </div>
              <BodyText className="text-center">
                Resize your browser window to see the responsive behavior in action!
              </BodyText>
            </div>
          </ModernCard>
        </Section>
      </Container>

      {/* Mobile Bottom Navigation */}
      <MobileBottomNav 
        items={navItems.map(item => ({
          ...item,
          onClick: () => setActiveTab(item.label.toLowerCase())
        }))}
      />

      {/* Floating Action Button */}
      <MobileFAB
        icon={<PlusIcon />}
        onClick={() => alert('Add new item')}
        position="bottom-right"
      />

      {/* Mobile Action Sheet */}
      <MobileActionSheet
        isOpen={showActionSheet}
        onClose={() => setShowActionSheet(false)}
        title="Student Actions"
        actions={actionSheetActions}
      />
    </div>
  );
}
