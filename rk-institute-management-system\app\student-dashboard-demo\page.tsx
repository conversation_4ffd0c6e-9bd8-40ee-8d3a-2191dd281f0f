/**
 * Enhanced Student Dashboard Demo
 * 
 * Showcases the enhanced student portal components with improved UI/UX:
 * - Enhanced Student Stats Overview with modern cards and professional design
 * - Enhanced Quick Actions with gradient cards and micro-interactions
 * - Mobile-first responsive design with touch-friendly elements
 * - Professional color system and typography improvements
 */

'use client';

import React, { useState } from 'react';
import {
  Container,
  Section,
  PageTitle,
  BodyText,
  ProfessionalAlert
} from '@/components/ui';
import EnhancedStudentStatsOverview from '@/components/features/student-portal/EnhancedStudentStatsOverview';
import EnhancedStudentQuickActions from '@/components/features/student-portal/EnhancedStudentQuickActions';

// Mock data for demonstration
const mockStudentProfile = {
  id: 'STU2024001',
  firstName: 'Sarah',
  lastName: '<PERSON>',
  name: '<PERSON>',
  studentId: '*********',
  course: 'Computer Science',
  grade: 'A',
  email: '<EMAIL>',
  phone: '+91 98765 43210',
  family: {
    id: 'FAM001',
    name: '<PERSON>',
    contact<PERSON>erson: 'Mr. <PERSON>'
  },
  enrollmentDate: '2024-01-15',
  status: 'active'
};

const mockStats = {
  totalCourses: 6,
  totalServices: 3,
  currentMonthFee: 25000,
  outstandingDues: 5000,
  academicLogs: 24,
  achievements: 8,
  attendancePercentage: 92.5,
  completionRate: 85.2,
  gpa: 3.8
};

export default function StudentDashboardDemo() {
  const [activeTab, setActiveTab] = useState('overview');
  const [showAlert, setShowAlert] = useState(true);

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    // Simulate navigation
    console.log(`Navigating to: ${tabId}`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Container size="lg" className="py-6">
        {/* Header */}
        <Section className="mb-8">
          <PageTitle>Enhanced Student Dashboard</PageTitle>
          <BodyText className="mt-2">
            Experience the improved student portal with modern design, enhanced usability, 
            and mobile-first responsive optimization.
          </BodyText>
        </Section>

        {/* Demo Alert */}
        {showAlert && (
          <Section className="mb-8">
            <ProfessionalAlert 
              type="info" 
              title="Demo Mode"
              dismissible
              onDismiss={() => setShowAlert(false)}
            >
              This is a demonstration of the enhanced student dashboard components. 
              All data shown is mock data for preview purposes.
            </ProfessionalAlert>
          </Section>
        )}

        {/* Enhanced Student Stats Overview */}
        <Section className="mb-8">
          <EnhancedStudentStatsOverview
            studentProfile={mockStudentProfile}
            stats={mockStats}
            loading={false}
          />
        </Section>

        {/* Enhanced Quick Actions */}
        <Section className="mb-8">
          <EnhancedStudentQuickActions
            stats={mockStats}
            onTabChange={handleTabChange}
          />
        </Section>

        {/* Current Tab Display */}
        <Section className="mb-8">
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              Current Tab: {activeTab}
            </h3>
            <div className="space-y-4">
              {activeTab === 'overview' && (
                <div>
                  <p className="text-gray-600">
                    You're viewing the main dashboard overview with stats and quick actions.
                  </p>
                </div>
              )}
              
              {activeTab === 'my-courses' && (
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">My Courses</h4>
                  <p className="text-gray-600 mb-4">
                    Here you would see all enrolled courses, study materials, and course progress.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <h5 className="font-medium text-blue-900">Data Structures</h5>
                      <p className="text-blue-700 text-sm">Progress: 85%</p>
                    </div>
                    <div className="p-4 bg-green-50 rounded-lg">
                      <h5 className="font-medium text-green-900">Web Development</h5>
                      <p className="text-green-700 text-sm">Progress: 92%</p>
                    </div>
                  </div>
                </div>
              )}
              
              {activeTab === 'assignments' && (
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Assignments & Tasks</h4>
                  <p className="text-gray-600 mb-4">
                    View and submit assignments, track deadlines, and access study materials.
                  </p>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                      <div>
                        <h5 className="font-medium text-yellow-900">Algorithm Analysis</h5>
                        <p className="text-yellow-700 text-sm">Due: Tomorrow</p>
                      </div>
                      <span className="px-2 py-1 bg-yellow-200 text-yellow-800 text-xs rounded">Urgent</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                      <div>
                        <h5 className="font-medium text-blue-900">React Project</h5>
                        <p className="text-blue-700 text-sm">Due: Next Week</p>
                      </div>
                      <span className="px-2 py-1 bg-blue-200 text-blue-800 text-xs rounded">Pending</span>
                    </div>
                  </div>
                </div>
              )}
              
              {activeTab === 'my-fees' && (
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Fee Management</h4>
                  <p className="text-gray-600 mb-4">
                    Manage your fee payments, view payment history, and download receipts.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-4 bg-red-50 rounded-lg">
                      <h5 className="font-medium text-red-900">Outstanding Dues</h5>
                      <p className="text-red-700 text-lg font-bold">₹5,000</p>
                      <p className="text-red-600 text-sm">Due Date: March 15, 2024</p>
                    </div>
                    <div className="p-4 bg-green-50 rounded-lg">
                      <h5 className="font-medium text-green-900">Last Payment</h5>
                      <p className="text-green-700 text-lg font-bold">₹20,000</p>
                      <p className="text-green-600 text-sm">Paid on: February 10, 2024</p>
                    </div>
                  </div>
                </div>
              )}
              
              {activeTab === 'academic-logs' && (
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Academic Progress</h4>
                  <p className="text-gray-600 mb-4">
                    Track your academic performance, view grades, and monitor progress.
                  </p>
                  <div className="space-y-4">
                    <div className="p-4 bg-purple-50 rounded-lg">
                      <h5 className="font-medium text-purple-900">Overall GPA</h5>
                      <p className="text-purple-700 text-2xl font-bold">3.8</p>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="p-3 bg-blue-50 rounded-lg text-center">
                        <p className="text-blue-900 font-medium">Attendance</p>
                        <p className="text-blue-700 text-xl font-bold">92.5%</p>
                      </div>
                      <div className="p-3 bg-green-50 rounded-lg text-center">
                        <p className="text-green-900 font-medium">Completion</p>
                        <p className="text-green-700 text-xl font-bold">85.2%</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              {!['overview', 'my-courses', 'assignments', 'my-fees', 'academic-logs'].includes(activeTab) && (
                <div>
                  <p className="text-gray-600">
                    This section would contain content for: <strong>{activeTab}</strong>
                  </p>
                  <p className="text-gray-500 text-sm mt-2">
                    In a real application, this would navigate to the appropriate page or component.
                  </p>
                </div>
              )}
            </div>
          </div>
        </Section>

        {/* Enhancement Summary */}
        <Section>
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-xl">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">UI/UX Enhancements Applied</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Design Improvements</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Modern typography with professional font hierarchy</li>
                  <li>• Advanced card design with sophisticated shadows</li>
                  <li>• Professional color system with gradients</li>
                  <li>• Enhanced micro-interactions and hover effects</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">User Experience</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Mobile-first responsive optimization</li>
                  <li>• Touch-friendly interactive elements</li>
                  <li>• Improved accessibility and focus states</li>
                  <li>• Enhanced loading states and animations</li>
                </ul>
              </div>
            </div>
          </div>
        </Section>
      </Container>
    </div>
  );
}
