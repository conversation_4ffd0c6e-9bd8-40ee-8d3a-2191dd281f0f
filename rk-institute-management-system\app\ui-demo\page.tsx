/**
 * UI/UX Enhancement Demo Page
 * 
 * Showcases the enhanced typography system and modern card components
 * implemented for the RK Institute Management System.
 */

'use client';

import React from 'react';
import {
  Container,
  Section,
  CardGrid,
  PageTitle,
  SectionTitle,
  CardTitle,
  BodyText,
  LeadText,
  Caption,
  StatusText,
  ModernCard,
  ModernStatsCard,
  ModernFeatureCard,
  ModernMetricCard,
  ModernActionCard
} from '@/components/ui';

// Demo icons (you can replace with your preferred icon library)
const UserIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
  </svg>
);

const BookIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
  </svg>
);

const CashIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);

const ChartIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
  </svg>
);

export default function UIDemo() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <Container size="xl" className="py-12">
        {/* Header Section */}
        <Section className="text-center mb-16">
          <PageTitle>RK Institute UI/UX Enhancement</PageTitle>
          <LeadText className="max-w-3xl mx-auto">
            Experience our enhanced typography system and modern card components designed 
            for optimal readability, accessibility, and user engagement across all devices.
          </LeadText>
          <Caption>Enhanced with Modern Design Principles</Caption>
        </Section>

        {/* Typography Showcase */}
        <Section className="mb-16">
          <SectionTitle>Enhanced Typography System</SectionTitle>
          <ModernCard variant="elevated" size="lg" className="mb-8">
            <div className="space-y-6">
              <div>
                <h1 className="text-4xl font-heading font-bold text-gray-900 mb-2">Display Heading</h1>
                <p className="text-gray-600">Perfect for hero sections and main page titles</p>
              </div>
              <div>
                <h2 className="text-3xl font-heading font-semibold text-gray-900 mb-2">Section Heading</h2>
                <p className="text-gray-600">Ideal for major section divisions</p>
              </div>
              <div>
                <h3 className="text-2xl font-heading font-semibold text-gray-900 mb-2">Card Title</h3>
                <p className="text-gray-600">Great for card headers and subsections</p>
              </div>
              <div>
                <BodyText>
                  This is our enhanced body text with improved readability. The Inter font family 
                  provides excellent legibility across all screen sizes, while the optimized line 
                  height and spacing ensure comfortable reading experiences.
                </BodyText>
              </div>
              <div className="flex flex-wrap gap-4">
                <StatusText status="success">Success Message</StatusText>
                <StatusText status="warning">Warning Message</StatusText>
                <StatusText status="error">Error Message</StatusText>
                <StatusText status="info">Info Message</StatusText>
              </div>
            </div>
          </ModernCard>
        </Section>

        {/* Modern Stats Cards */}
        <Section className="mb-16">
          <SectionTitle>Modern Statistics Cards</SectionTitle>
          <CardGrid columns={{ mobile: 1, tablet: 2, desktop: 4 }} gap="lg">
            <ModernStatsCard
              title="Total Students"
              value="1,247"
              change={{ value: "+12.5%", type: "increase" }}
              icon={<UserIcon />}
              color="blue"
            />
            <ModernStatsCard
              title="Active Courses"
              value="89"
              change={{ value: "****%", type: "increase" }}
              icon={<BookIcon />}
              color="green"
            />
            <ModernStatsCard
              title="Monthly Revenue"
              value="₹2,45,000"
              change={{ value: "****%", type: "increase" }}
              icon={<CashIcon />}
              color="purple"
            />
            <ModernStatsCard
              title="Completion Rate"
              value="94.2%"
              change={{ value: "-2.1%", type: "decrease" }}
              icon={<ChartIcon />}
              color="orange"
            />
          </CardGrid>
        </Section>

        {/* Modern Feature Cards */}
        <Section className="mb-16">
          <SectionTitle>Modern Feature Cards</SectionTitle>
          <CardGrid columns={{ mobile: 1, tablet: 2, desktop: 3 }} gap="lg">
            <ModernFeatureCard
              title="Student Management"
              description="Comprehensive student enrollment, profile management, and academic tracking system with real-time updates and detailed analytics."
              icon={<UserIcon />}
              badge={{ text: "Popular", color: "blue" }}
              onClick={() => alert('Navigate to Student Management')}
            />
            <ModernFeatureCard
              title="Course Administration"
              description="Complete course creation, scheduling, and management tools with integrated assignment tracking and grading systems."
              icon={<BookIcon />}
              badge={{ text: "New", color: "green" }}
              onClick={() => alert('Navigate to Course Administration')}
            />
            <ModernFeatureCard
              title="Financial Management"
              description="Advanced fee collection, payment tracking, and financial reporting with automated billing and receipt generation."
              icon={<CashIcon />}
              badge={{ text: "Enhanced", color: "purple" }}
              onClick={() => alert('Navigate to Financial Management')}
            />
          </CardGrid>
        </Section>

        {/* Modern Metric Cards */}
        <Section className="mb-16">
          <SectionTitle>Modern Metric Cards</SectionTitle>
          <CardGrid columns={{ mobile: 2, tablet: 4, desktop: 6 }} gap="md">
            <ModernMetricCard
              label="Enrollment Rate"
              value="87%"
              subValue="vs last month"
              trend="up"
              color="green"
            />
            <ModernMetricCard
              label="Course Completion"
              value="94.2%"
              subValue="average"
              trend="up"
              color="blue"
            />
            <ModernMetricCard
              label="Student Satisfaction"
              value="4.8/5"
              subValue="rating"
              trend="neutral"
              color="purple"
            />
            <ModernMetricCard
              label="Payment Collection"
              value="96%"
              subValue="on time"
              trend="up"
              color="green"
            />
            <ModernMetricCard
              label="Faculty Ratio"
              value="1:15"
              subValue="teacher:student"
              trend="neutral"
              color="orange"
            />
            <ModernMetricCard
              label="Resource Utilization"
              value="78%"
              subValue="capacity"
              trend="down"
              color="red"
            />
          </CardGrid>
        </Section>

        {/* Modern Action Cards */}
        <Section className="mb-16">
          <SectionTitle>Modern Action Cards</SectionTitle>
          <CardGrid columns={{ mobile: 1, tablet: 2, desktop: 3 }} gap="lg">
            <ModernActionCard
              title="Add New Student"
              description="Register a new student with complete profile setup"
              icon={<UserIcon />}
              color="blue"
              onClick={() => alert('Add New Student')}
            />
            <ModernActionCard
              title="Create Course"
              description="Set up a new course with curriculum and schedule"
              icon={<BookIcon />}
              color="green"
              onClick={() => alert('Create Course')}
            />
            <ModernActionCard
              title="Generate Report"
              description="Create comprehensive analytics and performance reports"
              icon={<ChartIcon />}
              color="purple"
              onClick={() => alert('Generate Report')}
            />
          </CardGrid>
        </Section>

        {/* Card Variants Showcase */}
        <Section className="mb-16">
          <SectionTitle>Card Design Variants</SectionTitle>
          <CardGrid columns={{ mobile: 1, tablet: 2, desktop: 3 }} gap="lg">
            <ModernCard variant="elevated" size="md">
              <CardTitle>Elevated Card</CardTitle>
              <BodyText>Features subtle shadows and depth for important content sections.</BodyText>
            </ModernCard>
            <ModernCard variant="glass" size="md">
              <CardTitle>Glass Card</CardTitle>
              <BodyText>Modern glassmorphism effect with backdrop blur for contemporary designs.</BodyText>
            </ModernCard>
            <ModernCard variant="gradient" size="md">
              <CardTitle>Gradient Card</CardTitle>
              <BodyText>Subtle gradient backgrounds that add visual interest without distraction.</BodyText>
            </ModernCard>
          </CardGrid>
        </Section>

        {/* Footer */}
        <Section className="text-center">
          <ModernCard variant="glass" size="lg">
            <CardTitle>Ready to Experience the Enhancement?</CardTitle>
            <BodyText className="mb-6">
              These components are now integrated throughout the RK Institute Management System, 
              providing a consistent, professional, and user-friendly experience across all modules.
            </BodyText>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <ModernActionCard
                title="View Dashboard"
                icon={<ChartIcon />}
                color="blue"
                onClick={() => window.location.href = '/dashboard'}
              />
              <ModernActionCard
                title="Explore Features"
                icon={<BookIcon />}
                color="green"
                onClick={() => alert('Feature exploration coming soon!')}
              />
            </div>
          </ModernCard>
        </Section>
      </Container>
    </div>
  );
}
