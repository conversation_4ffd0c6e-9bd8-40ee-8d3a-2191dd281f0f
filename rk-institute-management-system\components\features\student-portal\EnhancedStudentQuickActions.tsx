/**
 * Enhanced Student Quick Actions Component
 * 
 * Modern redesign with enhanced UI/UX improvements:
 * - Professional gradient cards with sophisticated color schemes
 * - Improved hover effects and micro-interactions
 * - Mobile-first responsive design with touch-friendly elements
 * - Enhanced accessibility with proper focus states
 * - Professional icon system integration
 * - Conditional highlighting for urgent actions
 */

'use client';

import { StudentQuickActionsProps } from './types';
import { 
  ProfessionalCard,
  HeadingText,
  BodyText,
  StatusBadge,
  Section,
  MobileButton
} from '@/components/ui';
import { ProfessionalIcon } from '@/components/ui/icons/ProfessionalIconSystem';

interface EnhancedQuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  gradient: 'brand' | 'ocean' | 'sunset' | 'forest' | 'royal' | 'cosmic';
  urgent?: boolean;
  badge?: {
    text: string;
    status: 'success' | 'warning' | 'error' | 'info' | 'neutral';
  };
}

export default function EnhancedStudentQuickActions({
  stats,
  onTabChange
}: StudentQuickActionsProps) {

  const quickActions: EnhancedQuickAction[] = [
    {
      id: 'my-courses',
      title: 'My Courses',
      description: `Access ${stats.totalCourses} enrolled courses and study materials`,
      icon: <ProfessionalIcon name="courses" size={28} className="text-white" />,
      gradient: 'brand',
      badge: {
        text: `${stats.totalCourses} Active`,
        status: 'info'
      }
    },
    {
      id: 'assignments',
      title: 'Assignments & Tasks',
      description: 'View homework, projects, and upcoming deadlines',
      icon: <ProfessionalIcon name="list" size={28} className="text-white" />,
      gradient: 'forest',
      badge: {
        text: 'Due Soon',
        status: 'warning'
      }
    },
    {
      id: 'my-fees',
      title: 'Fee Management',
      description: stats.outstandingDues > 0 
        ? `Pay outstanding dues of ₹${stats.outstandingDues.toLocaleString()}`
        : 'View payment history and fee structure',
      icon: <ProfessionalIcon name="fees" size={28} className="text-white" />,
      gradient: stats.outstandingDues > 0 ? 'sunset' : 'ocean',
      urgent: stats.outstandingDues > 0,
      badge: stats.outstandingDues > 0 
        ? {
            text: `₹${(stats.outstandingDues / 1000).toFixed(0)}K Due`,
            status: 'error'
          }
        : {
            text: 'Up to Date',
            status: 'success'
          }
    },
    {
      id: 'academic-logs',
      title: 'Academic Progress',
      description: 'Track your performance, grades, and achievements',
      icon: <ProfessionalIcon name="analytics" size={28} className="text-white" />,
      gradient: 'royal',
      badge: {
        text: `${stats.achievements} Awards`,
        status: 'success'
      }
    }
  ];

  const handleActionClick = (actionId: string) => {
    // Add smooth transition effect
    const element = document.getElementById(`action-${actionId}`);
    if (element) {
      element.style.transform = 'scale(0.95)';
      setTimeout(() => {
        element.style.transform = 'scale(1)';
        onTabChange(actionId);
      }, 150);
    } else {
      onTabChange(actionId);
    }
  };

  return (
    <Section>
      <div className="mb-6">
        <HeadingText level={2} className="mb-2">Quick Actions</HeadingText>
        <BodyText className="text-gray-600">
          Access your most important student tools and information
        </BodyText>
      </div>

      {/* Enhanced Action Cards Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
        {quickActions.map((action) => (
          <div
            key={action.id}
            id={`action-${action.id}`}
            className="group relative"
          >
            <ProfessionalCard 
              variant="gradient" 
              gradient={action.gradient}
              className="p-6 cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-xl"
              onClick={() => handleActionClick(action.id)}
            >
              {/* Urgent Indicator */}
              {action.urgent && (
                <div className="absolute -top-2 -right-2">
                  <div className="w-4 h-4 bg-red-500 rounded-full animate-pulse"></div>
                </div>
              )}

              {/* Card Content */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center mb-2">
                    <HeadingText level={3} className="text-white mr-3">
                      {action.title}
                    </HeadingText>
                    {action.badge && (
                      <StatusBadge 
                        status={action.badge.status} 
                        size="sm"
                        className="bg-white/20 backdrop-blur-sm border-white/30 text-white"
                      >
                        {action.badge.text}
                      </StatusBadge>
                    )}
                  </div>
                  <BodyText className="text-white/90 text-sm leading-relaxed">
                    {action.description}
                  </BodyText>
                </div>
                
                {/* Action Icon */}
                <div className="ml-4 p-3 bg-white/20 backdrop-blur-sm rounded-xl group-hover:bg-white/30 transition-colors">
                  {action.icon}
                </div>
              </div>

              {/* Action Button */}
              <div className="flex items-center justify-between">
                <MobileButton
                  variant="ghost"
                  size="sm"
                  className="bg-white/20 backdrop-blur-sm text-white border-white/30 hover:bg-white/30"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleActionClick(action.id);
                  }}
                >
                  Open
                </MobileButton>
                
                {/* Arrow Indicator */}
                <div className="text-white/60 group-hover:text-white/90 group-hover:translate-x-1 transition-all">
                  <ProfessionalIcon name="arrow-right" size={20} />
                </div>
              </div>
            </ProfessionalCard>
          </div>
        ))}
      </div>

      {/* Additional Quick Links */}
      <div className="mt-8 p-6 bg-gray-50 rounded-xl">
        <HeadingText level={3} className="mb-4">More Actions</HeadingText>
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
          <button 
            className="flex flex-col items-center p-3 bg-white rounded-lg hover:bg-gray-100 transition-colors group"
            onClick={() => onTabChange('attendance')}
          >
            <div className="p-2 bg-blue-100 rounded-lg mb-2 group-hover:bg-blue-200 transition-colors">
              <ProfessionalIcon name="calendar" size={20} className="text-blue-600" />
            </div>
            <BodyText className="text-sm font-medium text-center">Attendance</BodyText>
          </button>

          <button 
            className="flex flex-col items-center p-3 bg-white rounded-lg hover:bg-gray-100 transition-colors group"
            onClick={() => onTabChange('library')}
          >
            <div className="p-2 bg-green-100 rounded-lg mb-2 group-hover:bg-green-200 transition-colors">
              <ProfessionalIcon name="courses" size={20} className="text-green-600" />
            </div>
            <BodyText className="text-sm font-medium text-center">Library</BodyText>
          </button>

          <button 
            className="flex flex-col items-center p-3 bg-white rounded-lg hover:bg-gray-100 transition-colors group"
            onClick={() => onTabChange('support')}
          >
            <div className="p-2 bg-purple-100 rounded-lg mb-2 group-hover:bg-purple-200 transition-colors">
              <ProfessionalIcon name="support" size={20} className="text-purple-600" />
            </div>
            <BodyText className="text-sm font-medium text-center">Support</BodyText>
          </button>

          <button 
            className="flex flex-col items-center p-3 bg-white rounded-lg hover:bg-gray-100 transition-colors group"
            onClick={() => onTabChange('profile')}
          >
            <div className="p-2 bg-orange-100 rounded-lg mb-2 group-hover:bg-orange-200 transition-colors">
              <ProfessionalIcon name="user" size={20} className="text-orange-600" />
            </div>
            <BodyText className="text-sm font-medium text-center">Profile</BodyText>
          </button>
        </div>
      </div>
    </Section>
  );
}
