/**
 * Enhanced Student Stats Overview Component
 * 
 * Modern redesign of the student dashboard with enhanced UI/UX improvements:
 * - Professional typography system with improved hierarchy
 * - Advanced card design with sophisticated shadows and micro-interactions
 * - Mobile-first responsive optimization with touch-friendly elements
 * - Professional color system with sophisticated gradients
 * - Improved accessibility and visual hierarchy
 * - Enhanced loading states and smooth animations
 */

'use client';

import { StudentStatsOverviewProps } from './types';
import { 
  LoadingState, 
  ModernStatsCard,
  ProfessionalCard,
  StatusBadge,
  HeadingText,
  BodyText,
  Section
} from '@/components/ui';
import { ProfessionalIcon } from '@/components/ui/icons/ProfessionalIconSystem';

export default function EnhancedStudentStatsOverview({
  studentProfile,
  stats,
  loading
}: StudentStatsOverviewProps) {

  if (loading) {
    return <LoadingState message="Loading your dashboard..." />;
  }

  // Enhanced stat cards with modern design and professional colors
  const enhancedStatCards = [
    {
      title: 'Enrolled Courses',
      value: stats.totalCourses,
      change: { value: '+2', type: 'increase' as const },
      icon: <ProfessionalIcon name="courses" size={24} />,
      color: 'blue' as const
    },
    {
      title: 'Active Services',
      value: stats.totalServices,
      change: { value: '+1', type: 'increase' as const },
      icon: <ProfessionalIcon name="transport" size={24} />,
      color: 'green' as const
    },
    {
      title: 'Monthly Fee',
      value: `₹${stats.currentMonthFee.toLocaleString()}`,
      icon: <ProfessionalIcon name="fees" size={24} />,
      color: 'purple' as const
    },
    {
      title: 'Outstanding Dues',
      value: `₹${stats.outstandingDues.toLocaleString()}`,
      change: stats.outstandingDues > 0 ? { value: 'Due', type: 'neutral' as const } : { value: 'Paid', type: 'increase' as const },
      icon: <ProfessionalIcon name="fees" size={24} />,
      color: stats.outstandingDues > 0 ? 'red' as const : 'green' as const
    },
    {
      title: 'Academic Progress',
      value: stats.academicLogs,
      change: { value: '+5', type: 'increase' as const },
      icon: <ProfessionalIcon name="analytics" size={24} />,
      color: 'orange' as const
    },
    {
      title: 'Achievements',
      value: stats.achievements,
      change: { value: '+3', type: 'increase' as const },
      icon: <ProfessionalIcon name="graduation" size={24} />,
      color: 'yellow' as const
    }
  ];

  // Determine student status
  const getStudentStatus = () => {
    if (stats.outstandingDues > 0) return { status: 'warning' as const, label: 'Payment Due' };
    if (stats.totalCourses === 0) return { status: 'error' as const, label: 'No Courses' };
    return { status: 'success' as const, label: 'Active Student' };
  };

  const studentStatus = getStudentStatus();

  return (
    <div className="space-y-8">
      {/* Enhanced Welcome Banner with Professional Design */}
      <ProfessionalCard variant="gradient" gradient="brand" className="p-6 sm:p-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div className="flex-1">
            <HeadingText level={2} className="text-white mb-2">
              Welcome back, {studentProfile?.firstName || studentProfile?.name || 'Student'}!
            </HeadingText>
            <BodyText className="text-white/90 text-lg mb-4">
              {studentProfile?.course || 'Your Course'} • Grade {studentProfile?.grade || 'A'}
            </BodyText>
            
            {/* Student Info Badges */}
            <div className="flex flex-wrap gap-3 mb-4">
              <div className="bg-white/20 backdrop-blur-sm rounded-lg px-3 py-1.5">
                <BodyText className="text-white/90 text-sm font-medium">
                  ID: {studentProfile?.studentId || 'Loading...'}
                </BodyText>
              </div>
              {studentProfile?.family?.name && (
                <div className="bg-white/20 backdrop-blur-sm rounded-lg px-3 py-1.5">
                  <BodyText className="text-white/90 text-sm font-medium">
                    Family: {studentProfile.family.name}
                  </BodyText>
                </div>
              )}
            </div>
            
            {/* Status Badge */}
            <div className="inline-block">
              <StatusBadge 
                status={studentStatus.status} 
                size="md"
                className="bg-white/20 backdrop-blur-sm border-white/30"
              >
                {studentStatus.label}
              </StatusBadge>
            </div>
          </div>
          
          {/* Welcome Icon - Hidden on mobile for better space usage */}
          <div className="hidden sm:block text-6xl ml-6 opacity-80">
            🎓
          </div>
        </div>
      </ProfessionalCard>

      {/* Enhanced Stats Grid with Modern Cards */}
      <Section>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          {enhancedStatCards.map((stat, index) => (
            <ModernStatsCard
              key={index}
              title={stat.title}
              value={stat.value}
              change={stat.change}
              icon={stat.icon}
              color={stat.color}
            />
          ))}
        </div>
      </Section>

      {/* Additional Insights Section */}
      <Section>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Academic Progress Card */}
          <ProfessionalCard variant="elevated" className="p-6">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-blue-100 rounded-lg mr-3">
                <ProfessionalIcon name="analytics" size={20} className="text-blue-600" />
              </div>
              <HeadingText level={3}>Academic Progress</HeadingText>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <BodyText className="text-gray-600">Course Completion</BodyText>
                <BodyText className="font-semibold">85%</BodyText>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-blue-gradient h-2 rounded-full" style={{ width: '85%' }}></div>
              </div>
              <div className="flex justify-between items-center text-sm">
                <span className="text-gray-500">Target: 90%</span>
                <span className="text-blue-600 font-medium">On Track</span>
              </div>
            </div>
          </ProfessionalCard>

          {/* Quick Actions Card */}
          <ProfessionalCard variant="elevated" className="p-6">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-green-100 rounded-lg mr-3">
                <ProfessionalIcon name="list" size={20} className="text-green-600" />
              </div>
              <HeadingText level={3}>Quick Actions</HeadingText>
            </div>
            <div className="space-y-3">
              <button className="w-full text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                <div className="flex items-center justify-between">
                  <BodyText className="font-medium">View Assignments</BodyText>
                  <ProfessionalIcon name="arrow-right" size={16} className="text-gray-400" />
                </div>
              </button>
              <button className="w-full text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                <div className="flex items-center justify-between">
                  <BodyText className="font-medium">Check Attendance</BodyText>
                  <ProfessionalIcon name="arrow-right" size={16} className="text-gray-400" />
                </div>
              </button>
              <button className="w-full text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                <div className="flex items-center justify-between">
                  <BodyText className="font-medium">Download Reports</BodyText>
                  <ProfessionalIcon name="arrow-right" size={16} className="text-gray-400" />
                </div>
              </button>
            </div>
          </ProfessionalCard>
        </div>
      </Section>
    </div>
  );
}
