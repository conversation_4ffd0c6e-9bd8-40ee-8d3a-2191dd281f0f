/**
 * Mobile-First Responsive Optimization Components
 * 
 * Enhanced mobile experience with improved touch targets, better spacing,
 * and optimized layouts for all device sizes. Follows mobile-first design
 * principles with progressive enhancement.
 * 
 * Features:
 * - Touch-friendly 44px minimum targets
 * - Optimized spacing for mobile screens
 * - Progressive disclosure patterns
 * - Swipe-friendly interactions
 * - Improved readability on small screens
 */

import React from 'react';
import { cn } from '@/lib/utils';

// Professional Visual Components
export interface ProfessionalCardProps {
  children: React.ReactNode;
  variant?: 'standard' | 'elevated' | 'glass' | 'gradient';
  gradient?: 'brand' | 'ocean' | 'sunset' | 'forest' | 'royal' | 'cosmic';
  className?: string;
}

export interface StatusBadgeProps {
  children: React.ReactNode;
  status: 'success' | 'warning' | 'error' | 'info' | 'neutral';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export interface ProfessionalAlertProps {
  children: React.ReactNode;
  type: 'info' | 'success' | 'warning' | 'error';
  title?: string;
  dismissible?: boolean;
  onDismiss?: () => void;
  className?: string;
}

// Mobile-Optimized Button Props
export interface MobileButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  fullWidth?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  loading?: boolean;
  disabled?: boolean;
  className?: string;
  onClick?: () => void;
}

// Mobile-Optimized Input Props
export interface MobileInputProps {
  label?: string;
  placeholder?: string;
  value?: string;
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'search';
  error?: string;
  helper?: string;
  required?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
  className?: string;
  onChange?: (value: string) => void;
}

// Mobile Navigation Props
export interface MobileNavProps {
  items: Array<{
    label: string;
    icon?: React.ReactNode;
    href?: string;
    onClick?: () => void;
    active?: boolean;
    badge?: string | number;
  }>;
  className?: string;
}

// Mobile Card Stack Props
export interface MobileCardStackProps {
  children: React.ReactNode;
  spacing?: 'tight' | 'normal' | 'loose';
  className?: string;
}

// Style mappings
const buttonVariants = {
  primary: 'bg-primary-600 hover:bg-primary-700 text-white shadow-sm',
  secondary: 'bg-gray-100 hover:bg-gray-200 text-gray-900',
  outline: 'border-2 border-primary-600 text-primary-600 hover:bg-primary-50',
  ghost: 'text-primary-600 hover:bg-primary-50',
};

const buttonSizes = {
  sm: 'px-4 py-2 text-sm min-h-[40px]',
  md: 'px-6 py-3 text-base min-h-[44px]',
  lg: 'px-8 py-4 text-lg min-h-[48px]',
  xl: 'px-10 py-5 text-xl min-h-[52px]',
};

// Mobile-Optimized Button Component
export function MobileButton({
  children,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  icon,
  iconPosition = 'left',
  loading = false,
  disabled = false,
  className,
  onClick,
  ...props
}: MobileButtonProps) {
  const baseClasses = cn(
    'inline-flex items-center justify-center font-medium rounded-xl',
    'transition-all duration-200 focus:outline-none focus:ring-4 focus:ring-primary-500/20',
    'active:scale-95 touch-manipulation', // Mobile-specific optimizations
    buttonVariants[variant],
    buttonSizes[size],
    fullWidth && 'w-full',
    (disabled || loading) && 'opacity-50 cursor-not-allowed',
    className
  );

  return (
    <button
      className={baseClasses}
      onClick={disabled || loading ? undefined : onClick}
      disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
        </svg>
      )}
      {icon && iconPosition === 'left' && !loading && (
        <span className="mr-2">{icon}</span>
      )}
      {children}
      {icon && iconPosition === 'right' && !loading && (
        <span className="ml-2">{icon}</span>
      )}
    </button>
  );
}

// Mobile-Optimized Input Component
export function MobileInput({
  label,
  placeholder,
  value,
  type = 'text',
  error,
  helper,
  required = false,
  disabled = false,
  fullWidth = true,
  className,
  onChange,
  ...props
}: MobileInputProps) {
  const inputId = React.useId();

  return (
    <div className={cn('space-y-2', fullWidth && 'w-full', className)}>
      {label && (
        <label 
          htmlFor={inputId}
          className="block text-sm font-medium text-gray-700"
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      <input
        id={inputId}
        type={type}
        value={value}
        placeholder={placeholder}
        disabled={disabled}
        onChange={(e) => onChange?.(e.target.value)}
        className={cn(
          'block w-full px-4 py-3 text-base border border-gray-300 rounded-xl',
          'focus:outline-none focus:ring-4 focus:ring-primary-500/20 focus:border-primary-500',
          'transition-all duration-200 bg-white',
          'min-h-[44px] touch-manipulation', // Mobile optimizations
          error && 'border-red-500 focus:border-red-500 focus:ring-red-500/20',
          disabled && 'bg-gray-50 cursor-not-allowed opacity-50'
        )}
        {...props}
      />
      {error && (
        <p className="text-sm text-red-600 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          {error}
        </p>
      )}
      {helper && !error && (
        <p className="text-sm text-gray-500">{helper}</p>
      )}
    </div>
  );
}

// Mobile Bottom Navigation Component
export function MobileBottomNav({ items, className }: MobileNavProps) {
  return (
    <nav className={cn(
      'fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50',
      'safe-area-inset-bottom', // iOS safe area support
      className
    )}>
      <div className="flex items-center justify-around px-2 py-2">
        {items.map((item, index) => (
          <button
            key={index}
            onClick={item.onClick}
            className={cn(
              'flex flex-col items-center justify-center px-3 py-2 min-w-[44px] min-h-[44px]',
              'rounded-lg transition-all duration-200 touch-manipulation',
              'focus:outline-none focus:ring-2 focus:ring-primary-500/20',
              item.active 
                ? 'text-primary-600 bg-primary-50' 
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            )}
          >
            <div className="relative">
              {item.icon && (
                <div className="w-6 h-6 mb-1">
                  {item.icon}
                </div>
              )}
              {item.badge && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {item.badge}
                </span>
              )}
            </div>
            <span className="text-xs font-medium truncate max-w-[60px]">
              {item.label}
            </span>
          </button>
        ))}
      </div>
    </nav>
  );
}

// Mobile Card Stack Component
export function MobileCardStack({ 
  children, 
  spacing = 'normal', 
  className 
}: MobileCardStackProps) {
  const spacingClasses = {
    tight: 'space-y-2',
    normal: 'space-y-4',
    loose: 'space-y-6',
  };

  return (
    <div className={cn(
      'flex flex-col',
      spacingClasses[spacing],
      className
    )}>
      {children}
    </div>
  );
}

// Mobile-Optimized List Item Component
export interface MobileListItemProps {
  title: string;
  subtitle?: string;
  icon?: React.ReactNode;
  rightContent?: React.ReactNode;
  onClick?: () => void;
  href?: string;
  className?: string;
}

export function MobileListItem({
  title,
  subtitle,
  icon,
  rightContent,
  onClick,
  href,
  className
}: MobileListItemProps) {
  const content = (
    <div className={cn(
      'flex items-center p-4 bg-white border border-gray-200 rounded-xl',
      'min-h-[60px] touch-manipulation transition-all duration-200',
      'focus:outline-none focus:ring-4 focus:ring-primary-500/20',
      (onClick || href) && 'hover:bg-gray-50 active:scale-[0.98] cursor-pointer',
      className
    )}>
      {icon && (
        <div className="flex-shrink-0 w-10 h-10 mr-3 flex items-center justify-center bg-gray-100 rounded-lg">
          {icon}
        </div>
      )}
      <div className="flex-1 min-w-0">
        <p className="text-base font-medium text-gray-900 truncate">
          {title}
        </p>
        {subtitle && (
          <p className="text-sm text-gray-500 truncate">
            {subtitle}
          </p>
        )}
      </div>
      {rightContent && (
        <div className="flex-shrink-0 ml-3">
          {rightContent}
        </div>
      )}
      {(onClick || href) && !rightContent && (
        <div className="flex-shrink-0 ml-3 text-gray-400">
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
          </svg>
        </div>
      )}
    </div>
  );

  if (href) {
    return (
      <a href={href} className="block">
        {content}
      </a>
    );
  }

  return (
    <button onClick={onClick} className="block w-full text-left">
      {content}
    </button>
  );
}

// Mobile-Optimized Action Sheet Component
export interface MobileActionSheetProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  actions: Array<{
    label: string;
    icon?: React.ReactNode;
    onClick: () => void;
    destructive?: boolean;
  }>;
}

export function MobileActionSheet({ 
  isOpen, 
  onClose, 
  title, 
  actions 
}: MobileActionSheetProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-end justify-center p-4 bg-black/50">
      <div className="w-full max-w-sm bg-white rounded-t-2xl shadow-xl animate-slide-up">
        {title && (
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          </div>
        )}
        <div className="py-2">
          {actions.map((action, index) => (
            <button
              key={index}
              onClick={() => {
                action.onClick();
                onClose();
              }}
              className={cn(
                'w-full flex items-center px-6 py-4 text-left min-h-[56px]',
                'transition-colors duration-200 touch-manipulation',
                'focus:outline-none focus:bg-gray-50',
                action.destructive 
                  ? 'text-red-600 hover:bg-red-50' 
                  : 'text-gray-900 hover:bg-gray-50'
              )}
            >
              {action.icon && (
                <div className="w-6 h-6 mr-3 flex-shrink-0">
                  {action.icon}
                </div>
              )}
              <span className="text-base font-medium">{action.label}</span>
            </button>
          ))}
        </div>
        <div className="p-4 border-t border-gray-200">
          <button
            onClick={onClose}
            className="w-full py-3 text-base font-medium text-gray-600 bg-gray-100 rounded-xl hover:bg-gray-200 transition-colors duration-200 min-h-[44px]"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
}

// Mobile-Optimized Floating Action Button
export interface MobileFABProps {
  icon: React.ReactNode;
  onClick: () => void;
  position?: 'bottom-right' | 'bottom-left' | 'bottom-center';
  size?: 'md' | 'lg';
  className?: string;
}

export function MobileFAB({ 
  icon, 
  onClick, 
  position = 'bottom-right',
  size = 'lg',
  className 
}: MobileFABProps) {
  const positionClasses = {
    'bottom-right': 'bottom-6 right-6',
    'bottom-left': 'bottom-6 left-6',
    'bottom-center': 'bottom-6 left-1/2 transform -translate-x-1/2',
  };

  const sizeClasses = {
    md: 'w-12 h-12',
    lg: 'w-14 h-14',
  };

  return (
    <button
      onClick={onClick}
      className={cn(
        'fixed z-40 flex items-center justify-center',
        'bg-primary-600 hover:bg-primary-700 text-white',
        'rounded-full shadow-lg hover:shadow-xl',
        'transition-all duration-200 focus:outline-none focus:ring-4 focus:ring-primary-500/20',
        'active:scale-95 touch-manipulation',
        positionClasses[position],
        sizeClasses[size],
        className
      )}
    >
      {icon}
    </button>
  );
}

// Professional Visual Components
export function ProfessionalCard({
  children,
  variant = 'standard',
  gradient = 'brand',
  className
}: ProfessionalCardProps) {
  const variantClasses = {
    standard: 'card-professional',
    elevated: 'card-professional-elevated',
    glass: 'glass-card',
    gradient: `bg-${gradient}-gradient text-white`,
  };

  return (
    <div className={cn(variantClasses[variant], className)}>
      {children}
    </div>
  );
}

export function StatusBadge({
  children,
  status,
  size = 'md',
  className
}: StatusBadgeProps) {
  const statusClasses = {
    success: 'badge-professional-success',
    warning: 'badge-professional-warning',
    error: 'badge-professional-error',
    info: 'badge-professional-primary',
    neutral: 'bg-gray-100 text-gray-700',
  };

  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1 text-sm',
    lg: 'px-4 py-2 text-base',
  };

  return (
    <span className={cn(
      'badge-professional',
      statusClasses[status],
      sizeClasses[size],
      className
    )}>
      {children}
    </span>
  );
}

export function ProfessionalAlert({
  children,
  type,
  title,
  dismissible = false,
  onDismiss,
  className
}: ProfessionalAlertProps) {
  const typeClasses = {
    info: 'alert-professional-info',
    success: 'alert-professional-success',
    warning: 'alert-professional-warning',
    error: 'alert-professional-error',
  };

  const icons = {
    info: (
      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
      </svg>
    ),
    success: (
      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
      </svg>
    ),
    warning: (
      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
      </svg>
    ),
    error: (
      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
      </svg>
    ),
  };

  return (
    <div className={cn('alert-professional', typeClasses[type], className)}>
      <div className="flex items-start">
        <div className="flex-shrink-0 mr-3">
          {icons[type]}
        </div>
        <div className="flex-1">
          {title && (
            <h4 className="font-semibold mb-1">{title}</h4>
          )}
          <div>{children}</div>
        </div>
        {dismissible && (
          <button
            onClick={onDismiss}
            className="flex-shrink-0 ml-3 p-1 rounded-md hover:bg-black/10 transition-colors"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        )}
      </div>
    </div>
  );
}
