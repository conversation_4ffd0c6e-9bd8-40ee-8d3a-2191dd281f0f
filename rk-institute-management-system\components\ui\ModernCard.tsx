/**
 * Modern Card Design System
 * 
 * Next-generation card components with enhanced visual hierarchy,
 * modern shadows, improved spacing, and sophisticated micro-interactions.
 * 
 * Features:
 * - Multiple design variants (elevated, flat, outlined, glass)
 * - Advanced shadow system with depth layers
 * - Smooth micro-interactions and hover effects
 * - Responsive padding and spacing
 * - Professional visual hierarchy
 * - Accessibility optimized
 */

import React from 'react';
import { cn } from '@/lib/utils';

// Base Modern Card Props
export interface ModernCardProps {
  children: React.ReactNode;
  variant?: 'elevated' | 'flat' | 'outlined' | 'glass' | 'gradient';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  hover?: boolean;
  interactive?: boolean;
  className?: string;
  onClick?: () => void;
  as?: 'div' | 'section' | 'article' | 'aside';
}

// Stats Card Props
export interface ModernStatsCardProps {
  title: string;
  value: string | number;
  change?: {
    value: string;
    type: 'increase' | 'decrease' | 'neutral';
  };
  icon?: React.ReactNode;
  color?: 'blue' | 'green' | 'purple' | 'orange' | 'red' | 'gray';
  className?: string;
}

// Feature Card Props
export interface ModernFeatureCardProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  image?: string;
  href?: string;
  onClick?: () => void;
  badge?: {
    text: string;
    color: 'blue' | 'green' | 'purple' | 'orange' | 'red';
  };
  className?: string;
}

// Style mappings
const cardVariants = {
  elevated: 'bg-white shadow-soft hover:shadow-medium border border-gray-100',
  flat: 'bg-white border border-gray-200',
  outlined: 'bg-transparent border-2 border-gray-200 hover:border-gray-300',
  glass: 'bg-white/80 backdrop-blur-md border border-white/20 shadow-soft',
  gradient: 'bg-gradient-to-br from-white to-gray-50 shadow-soft border border-gray-100',
};

const cardSizes = {
  sm: 'p-4 rounded-xl',
  md: 'p-6 rounded-2xl',
  lg: 'p-8 rounded-2xl',
  xl: 'p-10 rounded-3xl',
};

const hoverEffects = {
  subtle: 'transition-all duration-300 hover:shadow-medium hover:-translate-y-1',
  strong: 'transition-all duration-300 hover:shadow-strong hover:-translate-y-2',
  glow: 'transition-all duration-300 hover:shadow-glow hover:-translate-y-1',
};

// Base Modern Card Component
export function ModernCard({
  children,
  variant = 'elevated',
  size = 'md',
  hover = true,
  interactive = false,
  className,
  onClick,
  as: Component = 'div',
  ...props
}: ModernCardProps) {
  const baseClasses = cn(
    cardVariants[variant],
    cardSizes[size],
    hover && hoverEffects.subtle,
    interactive && 'cursor-pointer focus:outline-none focus:ring-4 focus:ring-primary-500/20',
    onClick && 'cursor-pointer',
    className
  );

  return (
    <Component
      className={baseClasses}
      onClick={onClick}
      tabIndex={interactive ? 0 : undefined}
      role={interactive ? 'button' : undefined}
      {...props}
    >
      {children}
    </Component>
  );
}

// Modern Stats Card Component
export function ModernStatsCard({
  title,
  value,
  change,
  icon,
  color = 'blue',
  className
}: ModernStatsCardProps) {
  const colorSchemes = {
    blue: {
      icon: 'bg-blue-500 text-white',
      change: {
        increase: 'text-blue-600 bg-blue-50',
        decrease: 'text-red-600 bg-red-50',
        neutral: 'text-gray-600 bg-gray-50',
      }
    },
    green: {
      icon: 'bg-green-500 text-white',
      change: {
        increase: 'text-green-600 bg-green-50',
        decrease: 'text-red-600 bg-red-50',
        neutral: 'text-gray-600 bg-gray-50',
      }
    },
    purple: {
      icon: 'bg-purple-500 text-white',
      change: {
        increase: 'text-purple-600 bg-purple-50',
        decrease: 'text-red-600 bg-red-50',
        neutral: 'text-gray-600 bg-gray-50',
      }
    },
    orange: {
      icon: 'bg-orange-500 text-white',
      change: {
        increase: 'text-orange-600 bg-orange-50',
        decrease: 'text-red-600 bg-red-50',
        neutral: 'text-gray-600 bg-gray-50',
      }
    },
    red: {
      icon: 'bg-red-500 text-white',
      change: {
        increase: 'text-red-600 bg-red-50',
        decrease: 'text-red-600 bg-red-50',
        neutral: 'text-gray-600 bg-gray-50',
      }
    },
    gray: {
      icon: 'bg-gray-500 text-white',
      change: {
        increase: 'text-gray-600 bg-gray-50',
        decrease: 'text-red-600 bg-red-50',
        neutral: 'text-gray-600 bg-gray-50',
      }
    },
  };

  const scheme = colorSchemes[color];

  return (
    <ModernCard className={cn('relative overflow-hidden', className)} hover>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className="text-3xl font-bold text-gray-900 mb-2">{value}</p>
          {change && (
            <div className={cn(
              'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
              scheme.change[change.type]
            )}>
              <span className="mr-1">
                {change.type === 'increase' ? '↗' : change.type === 'decrease' ? '↘' : '→'}
              </span>
              {change.value}
            </div>
          )}
        </div>
        {icon && (
          <div className={cn(
            'flex items-center justify-center w-12 h-12 rounded-xl shadow-sm',
            scheme.icon
          )}>
            {icon}
          </div>
        )}
      </div>
    </ModernCard>
  );
}

// Modern Feature Card Component
export function ModernFeatureCard({
  title,
  description,
  icon,
  image,
  href,
  onClick,
  badge,
  className
}: ModernFeatureCardProps) {
  const badgeColors = {
    blue: 'bg-blue-100 text-blue-800 border-blue-200',
    green: 'bg-green-100 text-green-800 border-green-200',
    purple: 'bg-purple-100 text-purple-800 border-purple-200',
    orange: 'bg-orange-100 text-orange-800 border-orange-200',
    red: 'bg-red-100 text-red-800 border-red-200',
  };

  const content = (
    <ModernCard 
      className={cn('group relative overflow-hidden', className)}
      hover
      interactive={!!(href || onClick)}
      onClick={onClick}
    >
      {/* Badge */}
      {badge && (
        <div className="absolute top-4 right-4 z-10">
          <span className={cn(
            'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border',
            badgeColors[badge.color]
          )}>
            {badge.text}
          </span>
        </div>
      )}

      {/* Image */}
      {image && (
        <div className="aspect-video w-full mb-4 rounded-xl overflow-hidden bg-gray-100">
          <img 
            src={image} 
            alt={title}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          />
        </div>
      )}

      {/* Content */}
      <div className="flex items-start space-x-4">
        {icon && !image && (
          <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl text-white shadow-sm group-hover:scale-110 transition-transform duration-300">
            {icon}
          </div>
        )}
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors duration-200">
            {title}
          </h3>
          <p className="text-gray-600 leading-relaxed">
            {description}
          </p>
        </div>
      </div>

      {/* Hover indicator */}
      {(href || onClick) && (
        <div className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <div className="w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center text-white text-sm">
            →
          </div>
        </div>
      )}
    </ModernCard>
  );

  if (href) {
    return (
      <a href={href} className="block">
        {content}
      </a>
    );
  }

  return content;
}

// Specialized Card Variants

export interface ModernMetricCardProps {
  label: string;
  value: string | number;
  subValue?: string;
  trend?: 'up' | 'down' | 'neutral';
  color?: 'blue' | 'green' | 'purple' | 'orange' | 'red';
  className?: string;
}

export function ModernMetricCard({
  label,
  value,
  subValue,
  trend,
  color = 'blue',
  className
}: ModernMetricCardProps) {
  const trendIcons = {
    up: '↗',
    down: '↘',
    neutral: '→'
  };

  const trendColors = {
    up: 'text-green-600',
    down: 'text-red-600',
    neutral: 'text-gray-600'
  };

  return (
    <ModernCard variant="elevated" size="md" className={cn('text-center', className)}>
      <div className="space-y-2">
        <p className="text-sm font-medium text-gray-600">{label}</p>
        <p className="text-2xl font-bold text-gray-900">{value}</p>
        {subValue && (
          <div className="flex items-center justify-center space-x-1">
            {trend && (
              <span className={cn('text-sm', trendColors[trend])}>
                {trendIcons[trend]}
              </span>
            )}
            <span className="text-sm text-gray-500">{subValue}</span>
          </div>
        )}
      </div>
    </ModernCard>
  );
}

export interface ModernActionCardProps {
  title: string;
  description?: string;
  icon: React.ReactNode;
  onClick: () => void;
  color?: 'blue' | 'green' | 'purple' | 'orange' | 'red';
  disabled?: boolean;
  className?: string;
}

export function ModernActionCard({
  title,
  description,
  icon,
  onClick,
  color = 'blue',
  disabled = false,
  className
}: ModernActionCardProps) {
  const colorSchemes = {
    blue: 'from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700',
    green: 'from-green-500 to-green-600 hover:from-green-600 hover:to-green-700',
    purple: 'from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700',
    orange: 'from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700',
    red: 'from-red-500 to-red-600 hover:from-red-600 hover:to-red-700',
  };

  return (
    <ModernCard
      variant="elevated"
      size="md"
      interactive
      onClick={disabled ? undefined : onClick}
      className={cn(
        'group cursor-pointer transition-all duration-300',
        disabled && 'opacity-50 cursor-not-allowed',
        className
      )}
    >
      <div className="flex items-center space-x-4">
        <div className={cn(
          'flex items-center justify-center w-12 h-12 rounded-xl text-white shadow-sm transition-all duration-300',
          `bg-gradient-to-br ${colorSchemes[color]}`,
          !disabled && 'group-hover:scale-110'
        )}>
          {icon}
        </div>
        <div className="flex-1">
          <h3 className="font-semibold text-gray-900 group-hover:text-primary-600 transition-colors duration-200">
            {title}
          </h3>
          {description && (
            <p className="text-sm text-gray-600 mt-1">{description}</p>
          )}
        </div>
        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <div className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center text-gray-600 text-sm">
            →
          </div>
        </div>
      </div>
    </ModernCard>
  );
}
