/**
 * Enhanced Typography Component System
 * 
 * Modern typography components with improved readability, accessibility,
 * and responsive design. Implements the RK Institute design system with
 * professional typography hierarchy.
 * 
 * Features:
 * - Semantic HTML elements
 * - Responsive font scaling
 * - Accessibility optimized
 * - Consistent visual hierarchy
 * - Mobile-first approach
 */

import React from 'react';
import { cn } from '@/lib/utils';

// Heading Component Props
export interface HeadingProps {
  children: React.ReactNode;
  level?: 1 | 2 | 3 | 4 | 5 | 6;
  size?: 'display' | '1' | '2' | '3' | '4' | '5' | '6';
  weight?: 'normal' | 'medium' | 'semibold' | 'bold' | 'extrabold';
  color?: 'primary' | 'secondary' | 'tertiary' | 'inverse' | 'accent';
  align?: 'left' | 'center' | 'right';
  className?: string;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'div' | 'span';
}

// Text Component Props
export interface TextProps {
  children: React.ReactNode;
  size?: 'xs' | 'sm' | 'base' | 'lg' | 'xl';
  weight?: 'light' | 'normal' | 'medium' | 'semibold' | 'bold';
  color?: 'primary' | 'secondary' | 'tertiary' | 'muted' | 'inverse' | 'accent' | 'success' | 'warning' | 'error';
  align?: 'left' | 'center' | 'right' | 'justify';
  className?: string;
  as?: 'p' | 'span' | 'div' | 'strong' | 'em';
}

// Style mappings
const headingSizes = {
  display: 'text-4xl sm:text-5xl lg:text-6xl font-extrabold tracking-tight',
  '1': 'text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tight',
  '2': 'text-2xl sm:text-3xl lg:text-4xl font-semibold tracking-tight',
  '3': 'text-xl sm:text-2xl lg:text-3xl font-semibold',
  '4': 'text-lg sm:text-xl lg:text-2xl font-medium',
  '5': 'text-base sm:text-lg lg:text-xl font-medium',
  '6': 'text-sm sm:text-base lg:text-lg font-semibold uppercase tracking-wide',
};

const headingWeights = {
  normal: 'font-normal',
  medium: 'font-medium',
  semibold: 'font-semibold',
  bold: 'font-bold',
  extrabold: 'font-extrabold',
};

const textSizes = {
  xs: 'text-xs',
  sm: 'text-sm',
  base: 'text-base',
  lg: 'text-lg',
  xl: 'text-xl',
};

const textWeights = {
  light: 'font-light',
  normal: 'font-normal',
  medium: 'font-medium',
  semibold: 'font-semibold',
  bold: 'font-bold',
};

const colors = {
  primary: 'text-gray-900',
  secondary: 'text-gray-700',
  tertiary: 'text-gray-600',
  muted: 'text-gray-500',
  inverse: 'text-white',
  accent: 'text-primary-600',
  success: 'text-success-600',
  warning: 'text-warning-600',
  error: 'text-error-600',
};

const alignments = {
  left: 'text-left',
  center: 'text-center',
  right: 'text-right',
  justify: 'text-justify',
};

// Enhanced Heading Component
export function Heading({
  children,
  level = 1,
  size,
  weight,
  color = 'primary',
  align = 'left',
  className,
  as,
  ...props
}: HeadingProps) {
  // Determine component tag
  const Component = as || (`h${level}` as keyof JSX.IntrinsicElements);
  
  // Determine size based on level if not explicitly provided
  const finalSize = size || level.toString() as keyof typeof headingSizes;
  
  // Build classes
  const classes = cn(
    'font-heading',
    headingSizes[finalSize],
    weight && headingWeights[weight],
    colors[color],
    alignments[align],
    'leading-tight mb-4',
    className
  );

  return (
    <Component className={classes} {...props}>
      {children}
    </Component>
  );
}

// Enhanced Text Component
export function Text({
  children,
  size = 'base',
  weight = 'normal',
  color = 'secondary',
  align = 'left',
  className,
  as: Component = 'p',
  ...props
}: TextProps) {
  const classes = cn(
    'font-body',
    textSizes[size],
    textWeights[weight],
    colors[color],
    alignments[align],
    'leading-relaxed',
    className
  );

  return (
    <Component className={classes} {...props}>
      {children}
    </Component>
  );
}

// Specialized Typography Components

export interface LeadTextProps extends Omit<TextProps, 'size'> {
  children: React.ReactNode;
}

export function LeadText({ children, className, ...props }: LeadTextProps) {
  return (
    <Text
      size="xl"
      className={cn('text-gray-600 leading-relaxed mb-6', className)}
      {...props}
    >
      {children}
    </Text>
  );
}

export interface CaptionProps extends Omit<TextProps, 'size' | 'weight'> {
  children: React.ReactNode;
}

export function Caption({ children, className, ...props }: CaptionProps) {
  return (
    <Text
      size="xs"
      weight="medium"
      color="muted"
      className={cn('uppercase tracking-wider', className)}
      as="span"
      {...props}
    >
      {children}
    </Text>
  );
}

export interface CodeTextProps {
  children: React.ReactNode;
  className?: string;
}

export function CodeText({ children, className }: CodeTextProps) {
  return (
    <code className={cn(
      'font-mono text-sm bg-gray-100 border border-gray-200 rounded px-2 py-1 text-gray-800',
      className
    )}>
      {children}
    </code>
  );
}

export interface LinkTextProps {
  children: React.ReactNode;
  href?: string;
  onClick?: () => void;
  className?: string;
  external?: boolean;
}

export function LinkText({ 
  children, 
  href, 
  onClick, 
  className, 
  external = false 
}: LinkTextProps) {
  const classes = cn(
    'text-primary-600 hover:text-primary-700 font-medium transition-colors duration-200',
    'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded',
    className
  );

  if (href) {
    return (
      <a 
        href={href}
        className={classes}
        target={external ? '_blank' : undefined}
        rel={external ? 'noopener noreferrer' : undefined}
      >
        {children}
      </a>
    );
  }

  return (
    <button onClick={onClick} className={classes}>
      {children}
    </button>
  );
}

// Status Text Components
export interface StatusTextProps extends Omit<TextProps, 'color'> {
  children: React.ReactNode;
  status: 'success' | 'warning' | 'error' | 'info';
}

export function StatusText({ children, status, className, ...props }: StatusTextProps) {
  const statusColors = {
    success: 'text-success-600',
    warning: 'text-warning-600',
    error: 'text-error-600',
    info: 'text-primary-600',
  };

  return (
    <Text
      weight="medium"
      className={cn(statusColors[status], className)}
      {...props}
    >
      {children}
    </Text>
  );
}

// Responsive Typography Utilities
export interface ResponsiveTextProps {
  children: React.ReactNode;
  mobile?: string;
  tablet?: string;
  desktop?: string;
  className?: string;
}

export function ResponsiveText({ 
  children, 
  mobile = 'text-sm', 
  tablet = 'text-base', 
  desktop = 'text-lg',
  className 
}: ResponsiveTextProps) {
  return (
    <div className={cn(mobile, `sm:${tablet}`, `lg:${desktop}`, className)}>
      {children}
    </div>
  );
}

// Typography Presets for Common Use Cases
export function PageTitle({ children, className }: { children: React.ReactNode; className?: string }) {
  return (
    <Heading 
      level={1} 
      size="1" 
      className={cn('mb-2', className)}
    >
      {children}
    </Heading>
  );
}

export function SectionTitle({ children, className }: { children: React.ReactNode; className?: string }) {
  return (
    <Heading 
      level={2} 
      size="2" 
      className={cn('mb-4', className)}
    >
      {children}
    </Heading>
  );
}

export function CardTitle({ children, className }: { children: React.ReactNode; className?: string }) {
  return (
    <Heading 
      level={3} 
      size="3" 
      className={cn('mb-2', className)}
    >
      {children}
    </Heading>
  );
}

export function BodyText({ children, className }: { children: React.ReactNode; className?: string }) {
  return (
    <Text 
      size="base" 
      className={cn('mb-4', className)}
    >
      {children}
    </Text>
  );
}
