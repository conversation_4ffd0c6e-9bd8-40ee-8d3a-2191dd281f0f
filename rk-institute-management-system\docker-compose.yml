# =============================================================================
# RK Institute Management System - Docker Compose Configuration
# =============================================================================

version: '3.8'

services:
  # =============================================================================
  # Application Service
  # =============================================================================
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: rk-institute-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://rk_user:${DB_PASSWORD}@db:5432/rk_institute_prod
      - REDIS_URL=redis://redis:6379
    env_file:
      - .env.production
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - uploads:/app/uploads
      - logs:/app/logs
    networks:
      - rk-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # =============================================================================
  # PostgreSQL Database Service
  # =============================================================================
  db:
    image: postgres:15-alpine
    container_name: rk-institute-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: rk_institute_prod
      POSTGRES_USER: rk_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - rk-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U rk_user -d rk_institute_prod"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # =============================================================================
  # Redis Cache Service
  # =============================================================================
  redis:
    image: redis:7-alpine
    container_name: rk-institute-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - rk-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 30s

  # =============================================================================
  # Nginx Reverse Proxy (Optional)
  # =============================================================================
  nginx:
    image: nginx:alpine
    container_name: rk-institute-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - logs:/var/log/nginx
    depends_on:
      - app
    networks:
      - rk-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # Backup Service (Optional)
  # =============================================================================
  backup:
    image: postgres:15-alpine
    container_name: rk-institute-backup
    restart: "no"
    environment:
      PGPASSWORD: ${DB_PASSWORD}
    volumes:
      - backups:/backups
      - ./scripts/backup.sh:/backup.sh:ro
    networks:
      - rk-network
    depends_on:
      - db
    command: /bin/sh -c "chmod +x /backup.sh && /backup.sh"
    profiles:
      - backup

# =============================================================================
# Volumes
# =============================================================================
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  uploads:
    driver: local
  logs:
    driver: local
  backups:
    driver: local

# =============================================================================
# Networks
# =============================================================================
networks:
  rk-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
