{"name": "rk-institute-management-system", "version": "2.0.0", "description": "RK Institute Management System - Production Optimized v2.0", "private": true, "scripts": {"dev": "next dev", "build": "prisma migrate deploy && prisma generate && next build", "start": "next start", "postinstall": "prisma generate", "db:migrate": "prisma migrate deploy", "db:generate": "prisma generate", "health": "node scripts/health-check.js", "db:seed": "tsx prisma/seed.ts"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@heroicons/react": "^2.2.0", "@modelcontextprotocol/sdk": "^1.13.2", "@modelcontextprotocol/server-filesystem": "^2025.3.28", "@modelcontextprotocol/server-slack": "^2025.4.25", "@octokit/rest": "^22.0.0", "@prisma/client": "^6.9.0", "@ramidecodes/mcp-server-notion": "^1.0.6", "@sentry/mcp-server": "^0.12.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.5", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@vercel/mcp-adapter": "^0.11.1", "autoprefixer": "^10.0.1", "bcryptjs": "^2.4.3", "clsx": "^2.1.1", "cors": "^2.8.5", "dotenv": "^17.0.1", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "next": "14.0.4", "node-cron": "^4.1.0", "node-fetch": "^3.3.2", "nodemailer": "^7.0.3", "postcss": "^8", "react": "^18.2.0", "react-dom": "^18.2.0", "react-feather": "^2.0.10", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.3.0", "typescript": "^5.3.3", "zod": "^3.22.4"}, "devDependencies": {"@playwright/test": "^1.53.1", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@types/jest": "^29.5.8", "eslint": "^8.55.0", "eslint-config-next": "14.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "playwright": "^1.53.1", "prettier": "^3.1.0", "prisma": "^6.9.0", "tsx": "^4.6.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["institute-management", "education", "fee-management", "student-management", "nextjs", "typescript", "postgresql"], "author": "RK Institute", "license": "PROPRIETARY"}