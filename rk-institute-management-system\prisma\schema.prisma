generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id             String   @id @default(uuid())
  email          String   @unique
  password       String
  name           String
  role           String   @default("ADMIN")
  isActive       Boolean  @default(true)
  lastLoginAt    DateTime?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  family         Family?       @relation(fields: [familyId], references: [id])
  familyId       String?
  teacherCourses Course[]      @relation("TeacherCourses")
  academicLogs   AcademicLog[]
  assignments    Assignment[]  @relation("TeacherAssignments")
  gradedSubmissions AssignmentSubmission[] @relation("GradedSubmissions")
}

// Role values: ADMIN, TEACHER, PARENT, STUDENT

model Family {
  id              String            @id @default(uuid())
  name            String
  address         String?
  phone           String?
  email           String?
  discountAmount  Float             @default(0)
  isActive        Boolean           @default(true)
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  users           User[]
  students        Student[]
  payments        Payment[]
  auditTrail      AuditTrail[]
  notifications   Notification[]
}

model Student {
  id                    String                 @id @default(uuid())
  name                  String
  grade                 String?
  dateOfBirth           DateTime?
  enrollmentDate        DateTime               @default(now())
  isActive              Boolean                @default(true)
  studentId             String?                @unique // Optional student ID number
  createdAt             DateTime               @default(now())
  updatedAt             DateTime               @updatedAt
  family                Family                 @relation(fields: [familyId], references: [id])
  familyId              String
  subscriptions         StudentSubscription[]
  feeAllocations        StudentFeeAllocation[]
  academicLogs          AcademicLog[]
  attendanceRecords     AttendanceRecord[]
  assignments           Assignment[]           @relation("StudentAssignments")
  submissions           AssignmentSubmission[]
}

model Course {
  id                String                @id @default(uuid())
  name              String
  description       String?
  grade             String?
  isActive          Boolean               @default(true)
  capacity          Int?                  // Maximum students
  createdAt         DateTime              @default(now())
  updatedAt         DateTime              @updatedAt
  teacher           User?                 @relation("TeacherCourses", fields: [teacherId], references: [id])
  teacherId         String?
  feeStructure      FeeStructure?
  subscriptions     StudentSubscription[]
  attendanceRecords AttendanceRecord[]    @relation("CourseAttendance")
}

model Service {
  id                String                @id @default(uuid())
  name              String
  description       String?
  createdAt         DateTime              @default(now())
  updatedAt         DateTime              @updatedAt
  feeStructure      FeeStructure?
  subscriptions     StudentSubscription[]
}

// BillingCycle values: MONTHLY, QUARTERLY, HALF_YEARLY, YEARLY

model FeeStructure {
  id                String          @id @default(uuid())
  amount            Float
  billingCycle      String          @default("MONTHLY")
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  course            Course?         @relation(fields: [courseId], references: [id])
  courseId          String?         @unique
  service           Service?        @relation(fields: [serviceId], references: [id])
  serviceId         String?         @unique
}

model StudentSubscription {
  id                String          @id @default(uuid())
  discountAmount    Float           @default(0)
  startDate         DateTime        @default(now())
  endDate           DateTime?
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  student           Student         @relation(fields: [studentId], references: [id])
  studentId         String
  course            Course?         @relation(fields: [courseId], references: [id])
  courseId          String?
  service           Service?        @relation(fields: [serviceId], references: [id])
  serviceId         String?
}

model StudentFeeAllocation {
  id                String          @id @default(uuid())
  month             DateTime
  year              Int
  grossAmount       Float
  discountAmount    Float
  netAmount         Float
  isPaid            Boolean         @default(false)
  status            String          @default("PENDING") // PENDING, PAID, OVERDUE, CANCELLED
  dueDate           DateTime?
  paidDate          DateTime?
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  student           Student         @relation(fields: [studentId], references: [id])
  studentId         String
  payment           Payment?        @relation(fields: [paymentId], references: [id])
  paymentId         String?

  @@unique([studentId, month, year])
}

model Payment {
  id                String                 @id @default(uuid())
  amount            Float
  paymentDate       DateTime               @default(now())
  paymentMethod     String?
  reference         String?
  createdAt         DateTime               @default(now())
  updatedAt         DateTime               @updatedAt
  family            Family                 @relation(fields: [familyId], references: [id])
  familyId          String
  feeAllocations    StudentFeeAllocation[]
}

model AuditTrail {
  id                String          @id @default(uuid())
  action            String
  details           String?
  performedAt       DateTime        @default(now())
  performedBy       String
  family            Family?         @relation(fields: [familyId], references: [id])
  familyId          String?
}

model AcademicLog {
  id                String          @id @default(uuid())
  title             String
  content           String
  logType           String          // ACHIEVEMENT, CONCERN, GENERAL, PROGRESS
  subject           String?
  isPrivate         Boolean         @default(false)
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  student           Student         @relation(fields: [studentId], references: [id])
  studentId         String
  teacher           User            @relation(fields: [teacherId], references: [id])
  teacherId         String
}

// New models for enhanced functionality

model AttendanceRecord {
  id                String          @id @default(uuid())
  date              DateTime        @default(now())
  status            String          // PRESENT, ABSENT, LATE, EXCUSED
  notes             String?
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  student           Student         @relation(fields: [studentId], references: [id])
  studentId         String
  course            Course?         @relation("CourseAttendance", fields: [courseId], references: [id])
  courseId          String?
}

model Notification {
  id                String          @id @default(uuid())
  title             String
  message           String
  type              String          // EMAIL, SMS, PUSH, IN_APP
  status            String          @default("PENDING") // PENDING, SENT, FAILED
  recipientEmail    String?
  recipientPhone    String?
  sentAt            DateTime?
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  family            Family?         @relation(fields: [familyId], references: [id])
  familyId          String?
}

model SystemSettings {
  id                String          @id @default(uuid())
  key               String          @unique
  value             String
  description       String?
  category          String          @default("GENERAL")
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
}

model Report {
  id                String          @id @default(uuid())
  name              String
  type              String          // WEEKLY, MONTHLY, OUTSTANDING, FINANCIAL, ACADEMIC, ATTENDANCE, CUSTOM
  category          String          @default("AUTOMATION") // AUTOMATION, MANUAL, SCHEDULED
  parameters        String?         // JSON string of report parameters
  data              String?         // JSON string of report data
  generatedBy       String
  filePath          String?
  fileSize          Int?            // File size in bytes
  format            String          @default("JSON") // JSON, PDF, EXCEL, CSV
  status            String          @default("GENERATING") // GENERATING, COMPLETED, FAILED
  executionTime     Int?            // Execution time in milliseconds
  errorMessage      String?         // Error details if failed
  downloadCount     Int             @default(0)
  isArchived        Boolean         @default(false)
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
}

// Assignment and Homework Management System

model Assignment {
  id              String            @id @default(uuid())
  title           String
  description     String
  subject         String
  assignmentType  String            @default("HOMEWORK") // HOMEWORK, PROJECT, QUIZ, EXAM, NOTE
  priority        String            @default("MEDIUM") // LOW, MEDIUM, HIGH, URGENT
  dueDate         DateTime?
  attachmentUrl   String?
  attachmentName  String?
  isActive        Boolean           @default(true)

  // Assignment targeting
  grade           String?           // For grade-specific assignments
  studentId       String?           // For student-specific assignments
  teacherId       String

  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  // Relations
  teacher         User              @relation("TeacherAssignments", fields: [teacherId], references: [id], onDelete: Cascade)
  student         Student?          @relation("StudentAssignments", fields: [studentId], references: [id], onDelete: Cascade)
  submissions     AssignmentSubmission[]

  @@map("assignments")
}

model AssignmentSubmission {
  id              String            @id @default(uuid())
  assignmentId    String
  studentId       String
  content         String?
  attachmentUrl   String?
  attachmentName  String?
  status          String            @default("PENDING") // PENDING, SUBMITTED, GRADED, LATE
  submittedAt     DateTime?
  grade           String?
  feedback        String?
  gradedAt        DateTime?
  gradedBy        String?

  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  // Relations
  assignment      Assignment        @relation(fields: [assignmentId], references: [id], onDelete: Cascade)
  student         Student           @relation(fields: [studentId], references: [id], onDelete: Cascade)
  grader          User?             @relation("GradedSubmissions", fields: [gradedBy], references: [id])

  @@unique([assignmentId, studentId])
  @@map("assignment_submissions")
}
