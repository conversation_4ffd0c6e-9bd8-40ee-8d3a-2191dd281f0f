#!/usr/bin/env node

/**
 * AI Synchronization Initialization Script
 * Sets up synchronized workflow between Augment Code AI and Gemini CLI
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Load environment variables from .env file
require('dotenv').config();

console.log('🚀 Initializing AI Synchronization for RK Institute Management System');
console.log('=' .repeat(70));

// Configuration paths
const CONFIG_PATH = path.join(__dirname, '..', '.ai-sync-config.json');
const GEMINI_CONFIG_PATH = path.join(__dirname, '..', '.gemini', 'config.json');

// Load configuration
let config;
try {
  config = JSON.parse(fs.readFileSync(CONFIG_PATH, 'utf8'));
  console.log('✅ AI Sync configuration loaded');
} catch (error) {
  console.error('❌ Failed to load AI sync configuration:', error.message);
  process.exit(1);
}

// Environment setup
function setupEnvironment() {
  console.log('\n📋 Environment Setup');
  console.log('-'.repeat(30));
  
  const requiredEnvVars = [
    'DATABASE_URL',
    'JWT_SECRET',
    'NEXT_PUBLIC_APP_URL'
  ];
  
  const optionalEnvVars = [
    'GEMINI_API_KEY',
    'SLACK_BOT_TOKEN', 
    'NOTION_API_KEY',
    'GITHUB_TOKEN',
    'SENTRY_DSN'
  ];
  
  // Check required variables
  const missingRequired = requiredEnvVars.filter(varName => !process.env[varName]);
  if (missingRequired.length > 0) {
    console.log('❌ Missing required environment variables:');
    missingRequired.forEach(varName => console.log(`   - ${varName}`));
    return false;
  }
  
  console.log('✅ All required environment variables present');
  
  // Check optional variables
  const presentOptional = optionalEnvVars.filter(varName => process.env[varName]);
  console.log(`✅ Optional variables configured: ${presentOptional.length}/${optionalEnvVars.length}`);
  
  return true;
}

// System health check
function performHealthCheck() {
  console.log('\n🏥 System Health Check');
  console.log('-'.repeat(30));
  
  try {
    // Check if Gemini CLI is installed
    execSync('gemini --version', { stdio: 'pipe' });
    console.log('✅ Gemini CLI installed and accessible');
    
    // Check Node.js version
    const nodeVersion = process.version;
    console.log(`✅ Node.js version: ${nodeVersion}`);
    
    // Check if project dependencies are installed
    if (fs.existsSync(path.join(__dirname, '..', 'node_modules'))) {
      console.log('✅ Project dependencies installed');
    } else {
      console.log('⚠️  Project dependencies not found - run npm install');
    }
    
    // Check database connection (if possible)
    console.log('✅ Basic system checks passed');
    
    return true;
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
    return false;
  }
}

// Initialize MCP servers
function initializeMCPServers() {
  console.log('\n🔧 MCP Server Initialization');
  console.log('-'.repeat(30));
  
  const mcpServers = config.aiSyncConfig.mcpServers;
  
  Object.entries(mcpServers).forEach(([serverName, serverConfig]) => {
    if (serverConfig.enabled) {
      console.log(`✅ ${serverName} server configured`);
    } else {
      console.log(`⚠️  ${serverName} server disabled`);
    }
  });
  
  return true;
}

// Production readiness assessment
function assessProductionReadiness() {
  console.log('\n📊 Production Readiness Assessment');
  console.log('-'.repeat(30));
  
  const criteria = config.aiSyncConfig.productionReadiness.criteria;
  
  // Basic checks
  const checks = [
    { name: 'TypeScript Configuration', status: fs.existsSync(path.join(__dirname, '..', 'tsconfig.json')) },
    { name: 'Package Configuration', status: fs.existsSync(path.join(__dirname, '..', 'package.json')) },
    { name: 'Database Schema', status: fs.existsSync(path.join(__dirname, '..', 'prisma', 'schema.prisma')) },
    { name: 'Docker Configuration', status: fs.existsSync(path.join(__dirname, '..', 'Dockerfile')) },
    { name: 'Environment Template', status: fs.existsSync(path.join(__dirname, '..', '.env.example')) }
  ];
  
  checks.forEach(check => {
    console.log(`${check.status ? '✅' : '❌'} ${check.name}`);
  });
  
  const passedChecks = checks.filter(check => check.status).length;
  const totalChecks = checks.length;
  
  console.log(`\n📈 Production Readiness Score: ${passedChecks}/${totalChecks} (${Math.round(passedChecks/totalChecks*100)}%)`);
  
  return passedChecks / totalChecks;
}

// Main initialization
async function main() {
  try {
    // Step 1: Environment setup
    if (!setupEnvironment()) {
      console.log('\n❌ Environment setup failed. Please configure required variables.');
      process.exit(1);
    }
    
    // Step 2: Health check
    if (!performHealthCheck()) {
      console.log('\n❌ Health check failed. Please resolve issues before continuing.');
      process.exit(1);
    }
    
    // Step 3: MCP server initialization
    if (!initializeMCPServers()) {
      console.log('\n❌ MCP server initialization failed.');
      process.exit(1);
    }
    
    // Step 4: Production readiness assessment
    const readinessScore = assessProductionReadiness();
    
    // Summary
    console.log('\n🎯 AI Synchronization Summary');
    console.log('='.repeat(70));
    console.log('✅ Augment Code AI: Ready for coordination');
    console.log('✅ Gemini CLI: Installed and configured');
    console.log(`📊 Production Readiness: ${Math.round(readinessScore * 100)}%`);
    
    if (readinessScore >= 0.8) {
      console.log('🚀 System ready for synchronized AI development!');
    } else {
      console.log('⚠️  Additional setup required for full production readiness');
    }
    
    console.log('\n📝 Next Steps:');
    console.log('1. Set GEMINI_API_KEY for higher rate limits');
    console.log('2. Configure MCP server environment variables');
    console.log('3. Run: cd rk-institute-management-system && gemini');
    console.log('4. Start synchronized development workflow');
    
  } catch (error) {
    console.error('❌ Initialization failed:', error.message);
    process.exit(1);
  }
}

// Run initialization
main();
