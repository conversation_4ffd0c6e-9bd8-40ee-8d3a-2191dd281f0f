#!/usr/bin/env node

/**
 * Production Validation Script
 * Comprehensive testing before production deployment
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Load environment variables
require('dotenv').config();

console.log('🔍 RK Institute Management System - Production Validation');
console.log('=' .repeat(70));

let validationScore = 0;
let totalChecks = 0;
const issues = [];

function runCheck(name, checkFunction) {
  totalChecks++;
  console.log(`\n🔍 ${name}`);
  console.log('-'.repeat(50));
  
  try {
    const result = checkFunction();
    if (result.success) {
      console.log(`✅ ${result.message}`);
      validationScore++;
    } else {
      console.log(`❌ ${result.message}`);
      issues.push(`${name}: ${result.message}`);
    }
  } catch (error) {
    console.log(`❌ ${name}: ${error.message}`);
    issues.push(`${name}: ${error.message}`);
  }
}

// Environment Variables Check
function checkEnvironmentVariables() {
  const required = [
    'DATABASE_URL',
    'JWT_SECRET', 
    'NEXT_PUBLIC_APP_URL'
  ];
  
  const missing = required.filter(varName => !process.env[varName]);
  
  if (missing.length === 0) {
    return { success: true, message: 'All required environment variables present' };
  } else {
    return { success: false, message: `Missing variables: ${missing.join(', ')}` };
  }
}

// File Structure Check
function checkFileStructure() {
  const requiredFiles = [
    'package.json',
    'next.config.js',
    'tsconfig.json',
    'prisma/schema.prisma',
    'Dockerfile',
    '.env.example'
  ];
  
  const missingFiles = requiredFiles.filter(file => 
    !fs.existsSync(path.join(__dirname, '..', file))
  );
  
  if (missingFiles.length === 0) {
    return { success: true, message: 'All required files present' };
  } else {
    return { success: false, message: `Missing files: ${missingFiles.join(', ')}` };
  }
}

// TypeScript Compilation Check
function checkTypeScriptCompilation() {
  try {
    execSync('npx tsc --noEmit', { stdio: 'pipe', cwd: path.join(__dirname, '..') });
    return { success: true, message: 'TypeScript compilation successful' };
  } catch (error) {
    return { success: false, message: 'TypeScript compilation errors found' };
  }
}

// Package Dependencies Check
function checkDependencies() {
  try {
    const packageJson = JSON.parse(
      fs.readFileSync(path.join(__dirname, '..', 'package.json'), 'utf8')
    );
    
    const criticalDeps = [
      'next',
      'react',
      'prisma',
      '@prisma/client',
      'jsonwebtoken',
      'bcryptjs'
    ];
    
    const missingDeps = criticalDeps.filter(dep => 
      !packageJson.dependencies[dep] && !packageJson.devDependencies[dep]
    );
    
    if (missingDeps.length === 0) {
      return { success: true, message: 'All critical dependencies present' };
    } else {
      return { success: false, message: `Missing dependencies: ${missingDeps.join(', ')}` };
    }
  } catch (error) {
    return { success: false, message: 'Failed to read package.json' };
  }
}

// Security Configuration Check
function checkSecurityConfiguration() {
  const jwtSecret = process.env.JWT_SECRET;
  
  if (!jwtSecret) {
    return { success: false, message: 'JWT_SECRET not configured' };
  }
  
  if (jwtSecret.length < 32) {
    return { success: false, message: 'JWT_SECRET should be at least 32 characters' };
  }
  
  if (jwtSecret.includes('CHANGE_THIS') || jwtSecret.includes('dev_')) {
    return { success: false, message: 'JWT_SECRET appears to be a placeholder' };
  }
  
  return { success: true, message: 'Security configuration validated' };
}

// Database Schema Check
function checkDatabaseSchema() {
  try {
    const schemaContent = fs.readFileSync(
      path.join(__dirname, '..', 'prisma', 'schema.prisma'), 
      'utf8'
    );
    
    // Check for PostgreSQL provider
    if (!schemaContent.includes('provider = "postgresql"')) {
      return { success: false, message: 'Database provider should be PostgreSQL for production' };
    }
    
    // Check for essential models
    const requiredModels = ['User', 'Student', 'Family', 'Course', 'FeeStructure'];
    const missingModels = requiredModels.filter(model => 
      !schemaContent.includes(`model ${model}`)
    );
    
    if (missingModels.length > 0) {
      return { success: false, message: `Missing models: ${missingModels.join(', ')}` };
    }
    
    return { success: true, message: 'Database schema validated' };
  } catch (error) {
    return { success: false, message: 'Failed to read database schema' };
  }
}

// API Routes Check
function checkAPIRoutes() {
  const apiDir = path.join(__dirname, '..', 'app', 'api');
  
  if (!fs.existsSync(apiDir)) {
    return { success: false, message: 'API directory not found' };
  }
  
  const requiredRoutes = [
    'auth',
    'students', 
    'health',
    'automation'
  ];
  
  const missingRoutes = requiredRoutes.filter(route => 
    !fs.existsSync(path.join(apiDir, route))
  );
  
  if (missingRoutes.length === 0) {
    return { success: true, message: 'All essential API routes present' };
  } else {
    return { success: false, message: `Missing API routes: ${missingRoutes.join(', ')}` };
  }
}

// Docker Configuration Check
function checkDockerConfiguration() {
  const dockerfilePath = path.join(__dirname, '..', 'Dockerfile');
  
  if (!fs.existsSync(dockerfilePath)) {
    return { success: false, message: 'Dockerfile not found' };
  }
  
  try {
    const dockerfileContent = fs.readFileSync(dockerfilePath, 'utf8');
    
    // Check for multi-stage build
    if (!dockerfileContent.includes('FROM node:') || !dockerfileContent.includes('WORKDIR')) {
      return { success: false, message: 'Dockerfile appears incomplete' };
    }
    
    return { success: true, message: 'Docker configuration validated' };
  } catch (error) {
    return { success: false, message: 'Failed to read Dockerfile' };
  }
}

// Production Build Check
function checkProductionBuild() {
  try {
    // Check if we can generate Prisma client
    execSync('npx prisma generate', { stdio: 'pipe', cwd: path.join(__dirname, '..') });
    
    return { success: true, message: 'Prisma client generation successful' };
  } catch (error) {
    return { success: false, message: 'Failed to generate Prisma client' };
  }
}

// Main validation function
async function runValidation() {
  console.log('Starting comprehensive production validation...\n');
  
  // Run all checks
  runCheck('Environment Variables', checkEnvironmentVariables);
  runCheck('File Structure', checkFileStructure);
  runCheck('TypeScript Compilation', checkTypeScriptCompilation);
  runCheck('Package Dependencies', checkDependencies);
  runCheck('Security Configuration', checkSecurityConfiguration);
  runCheck('Database Schema', checkDatabaseSchema);
  runCheck('API Routes', checkAPIRoutes);
  runCheck('Docker Configuration', checkDockerConfiguration);
  runCheck('Production Build', checkProductionBuild);
  
  // Calculate score
  const scorePercentage = Math.round((validationScore / totalChecks) * 100);
  
  // Display results
  console.log('\n' + '='.repeat(70));
  console.log('🎯 PRODUCTION VALIDATION RESULTS');
  console.log('='.repeat(70));
  console.log(`📊 Score: ${validationScore}/${totalChecks} (${scorePercentage}%)`);
  
  if (scorePercentage >= 90) {
    console.log('🚀 READY FOR PRODUCTION DEPLOYMENT!');
    console.log('✅ System meets production requirements');
  } else if (scorePercentage >= 75) {
    console.log('⚠️  MOSTLY READY - Minor issues to resolve');
    console.log('🔧 Address the following issues before deployment:');
  } else {
    console.log('❌ NOT READY FOR PRODUCTION');
    console.log('🛠️  Critical issues must be resolved:');
  }
  
  // Display issues
  if (issues.length > 0) {
    console.log('\n📋 Issues to Address:');
    issues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue}`);
    });
  }
  
  console.log('\n📝 Next Steps:');
  if (scorePercentage >= 90) {
    console.log('1. Set up production database (Neon PostgreSQL recommended)');
    console.log('2. Configure production environment variables');
    console.log('3. Deploy to Vercel or your preferred platform');
    console.log('4. Run post-deployment verification');
  } else {
    console.log('1. Resolve the issues listed above');
    console.log('2. Re-run this validation script');
    console.log('3. Proceed with deployment once score is 90%+');
  }
  
  console.log('\n🔗 Resources:');
  console.log('- Production Deployment Guide: ./PRODUCTION_DEPLOYMENT_GUIDE.md');
  console.log('- AI Sync Protocol: ./AI_SYNC_PROTOCOL.md');
  console.log('- System Documentation: ./docs/README.md');
}

// Run the validation
runValidation().catch(console.error);
