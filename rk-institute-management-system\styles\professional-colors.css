/**
 * Professional Color & Visual System
 * 
 * Advanced color palette, sophisticated gradients, and professional
 * visual elements for the RK Institute Management System.
 * 
 * Features:
 * - Extended professional color palette
 * - Sophisticated gradient systems
 * - Advanced visual effects
 * - Dark mode support
 * - Accessibility-compliant contrast ratios
 * - Brand-consistent color schemes
 */

/* Professional Color Variables */
:root {
  /* Primary Brand Colors */
  --color-brand-50: #eff6ff;
  --color-brand-100: #dbeafe;
  --color-brand-200: #bfdbfe;
  --color-brand-300: #93c5fd;
  --color-brand-400: #60a5fa;
  --color-brand-500: #3b82f6;
  --color-brand-600: #2563eb;
  --color-brand-700: #1d4ed8;
  --color-brand-800: #1e40af;
  --color-brand-900: #1e3a8a;

  /* Professional Neutrals */
  --color-neutral-0: #ffffff;
  --color-neutral-25: #fcfcfd;
  --color-neutral-50: #f9fafb;
  --color-neutral-100: #f2f4f7;
  --color-neutral-200: #eaecf0;
  --color-neutral-300: #d0d5dd;
  --color-neutral-400: #98a2b3;
  --color-neutral-500: #667085;
  --color-neutral-600: #475467;
  --color-neutral-700: #344054;
  --color-neutral-800: #1d2939;
  --color-neutral-900: #101828;

  /* Success Colors */
  --color-success-50: #ecfdf3;
  --color-success-100: #d1fadf;
  --color-success-200: #a6f4c5;
  --color-success-300: #6ce9a6;
  --color-success-400: #32d583;
  --color-success-500: #12b76a;
  --color-success-600: #039855;
  --color-success-700: #027a48;
  --color-success-800: #05603a;
  --color-success-900: #054f31;

  /* Warning Colors */
  --color-warning-50: #fffcf5;
  --color-warning-100: #fffaeb;
  --color-warning-200: #fef0c7;
  --color-warning-300: #fedf89;
  --color-warning-400: #fec84b;
  --color-warning-500: #fdb022;
  --color-warning-600: #f79009;
  --color-warning-700: #dc6803;
  --color-warning-800: #b54708;
  --color-warning-900: #93370d;

  /* Error Colors */
  --color-error-50: #fef3f2;
  --color-error-100: #fee4e2;
  --color-error-200: #fecdca;
  --color-error-300: #fda29b;
  --color-error-400: #f97066;
  --color-error-500: #f04438;
  --color-error-600: #d92d20;
  --color-error-700: #b42318;
  --color-error-800: #912018;
  --color-error-900: #7a271a;

  /* Professional Gradients */
  --gradient-brand: linear-gradient(135deg, var(--color-brand-500) 0%, var(--color-brand-600) 100%);
  --gradient-brand-subtle: linear-gradient(135deg, var(--color-brand-50) 0%, var(--color-brand-100) 100%);
  --gradient-success: linear-gradient(135deg, var(--color-success-500) 0%, var(--color-success-600) 100%);
  --gradient-warning: linear-gradient(135deg, var(--color-warning-500) 0%, var(--color-warning-600) 100%);
  --gradient-error: linear-gradient(135deg, var(--color-error-500) 0%, var(--color-error-600) 100%);
  
  /* Advanced Gradients */
  --gradient-ocean: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-sunset: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-forest: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-royal: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-cosmic: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  
  /* Glass Effects */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  --glass-backdrop: blur(4px);

  /* Professional Shadows */
  --shadow-xs: 0px 1px 2px rgba(16, 24, 40, 0.05);
  --shadow-sm: 0px 1px 3px rgba(16, 24, 40, 0.1), 0px 1px 2px rgba(16, 24, 40, 0.06);
  --shadow-md: 0px 4px 8px -2px rgba(16, 24, 40, 0.1), 0px 2px 4px -2px rgba(16, 24, 40, 0.06);
  --shadow-lg: 0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03);
  --shadow-xl: 0px 20px 24px -4px rgba(16, 24, 40, 0.08), 0px 8px 8px -4px rgba(16, 24, 40, 0.03);
  --shadow-2xl: 0px 24px 48px -12px rgba(16, 24, 40, 0.18);
  --shadow-3xl: 0px 32px 64px -12px rgba(16, 24, 40, 0.14);
}

/* Professional Color Classes */
.bg-brand-gradient {
  background: var(--gradient-brand);
}

.bg-brand-subtle-gradient {
  background: var(--gradient-brand-subtle);
}

.bg-success-gradient {
  background: var(--gradient-success);
}

.bg-warning-gradient {
  background: var(--gradient-warning);
}

.bg-error-gradient {
  background: var(--gradient-error);
}

.bg-ocean-gradient {
  background: var(--gradient-ocean);
}

.bg-sunset-gradient {
  background: var(--gradient-sunset);
}

.bg-forest-gradient {
  background: var(--gradient-forest);
}

.bg-royal-gradient {
  background: var(--gradient-royal);
}

.bg-cosmic-gradient {
  background: var(--gradient-cosmic);
}

/* Glass Morphism Effects */
.glass-card {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border-radius: 1rem;
}

.glass-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
}

/* Professional Shadow Classes */
.shadow-professional-xs { box-shadow: var(--shadow-xs); }
.shadow-professional-sm { box-shadow: var(--shadow-sm); }
.shadow-professional-md { box-shadow: var(--shadow-md); }
.shadow-professional-lg { box-shadow: var(--shadow-lg); }
.shadow-professional-xl { box-shadow: var(--shadow-xl); }
.shadow-professional-2xl { box-shadow: var(--shadow-2xl); }
.shadow-professional-3xl { box-shadow: var(--shadow-3xl); }

/* Status Indicators */
.status-success {
  background: var(--color-success-50);
  color: var(--color-success-700);
  border: 1px solid var(--color-success-200);
}

.status-warning {
  background: var(--color-warning-50);
  color: var(--color-warning-700);
  border: 1px solid var(--color-warning-200);
}

.status-error {
  background: var(--color-error-50);
  color: var(--color-error-700);
  border: 1px solid var(--color-error-200);
}

.status-info {
  background: var(--color-brand-50);
  color: var(--color-brand-700);
  border: 1px solid var(--color-brand-200);
}

/* Professional Buttons */
.btn-professional-primary {
  background: var(--gradient-brand);
  color: white;
  border: none;
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
}

.btn-professional-primary:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-professional-secondary {
  background: var(--color-neutral-0);
  color: var(--color-neutral-700);
  border: 1px solid var(--color-neutral-300);
  box-shadow: var(--shadow-xs);
  transition: all 0.2s ease;
}

.btn-professional-secondary:hover {
  background: var(--color-neutral-50);
  border-color: var(--color-neutral-400);
  box-shadow: var(--shadow-sm);
}

/* Professional Cards */
.card-professional {
  background: var(--color-neutral-0);
  border: 1px solid var(--color-neutral-200);
  border-radius: 0.75rem;
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
}

.card-professional:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-professional-elevated {
  background: var(--color-neutral-0);
  border: none;
  border-radius: 1rem;
  box-shadow: var(--shadow-lg);
  transition: all 0.3s ease;
}

.card-professional-elevated:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px);
}

/* Professional Inputs */
.input-professional {
  background: var(--color-neutral-0);
  border: 1px solid var(--color-neutral-300);
  border-radius: 0.5rem;
  color: var(--color-neutral-900);
  transition: all 0.2s ease;
}

.input-professional:focus {
  border-color: var(--color-brand-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

.input-professional::placeholder {
  color: var(--color-neutral-500);
}

/* Professional Navigation */
.nav-professional {
  background: var(--color-neutral-0);
  border-bottom: 1px solid var(--color-neutral-200);
  box-shadow: var(--shadow-sm);
}

.nav-professional-item {
  color: var(--color-neutral-600);
  transition: all 0.2s ease;
}

.nav-professional-item:hover,
.nav-professional-item.active {
  color: var(--color-brand-600);
  background: var(--color-brand-50);
}

/* Professional Tables */
.table-professional {
  background: var(--color-neutral-0);
  border: 1px solid var(--color-neutral-200);
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table-professional th {
  background: var(--color-neutral-50);
  color: var(--color-neutral-700);
  font-weight: 600;
  border-bottom: 1px solid var(--color-neutral-200);
}

.table-professional td {
  color: var(--color-neutral-900);
  border-bottom: 1px solid var(--color-neutral-100);
}

.table-professional tr:hover {
  background: var(--color-neutral-25);
}

/* Professional Badges */
.badge-professional {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1;
}

.badge-professional-primary {
  background: var(--color-brand-100);
  color: var(--color-brand-700);
}

.badge-professional-success {
  background: var(--color-success-100);
  color: var(--color-success-700);
}

.badge-professional-warning {
  background: var(--color-warning-100);
  color: var(--color-warning-700);
}

.badge-professional-error {
  background: var(--color-error-100);
  color: var(--color-error-700);
}

/* Professional Alerts */
.alert-professional {
  padding: 1rem;
  border-radius: 0.5rem;
  border-left: 4px solid;
}

.alert-professional-info {
  background: var(--color-brand-50);
  border-left-color: var(--color-brand-500);
  color: var(--color-brand-700);
}

.alert-professional-success {
  background: var(--color-success-50);
  border-left-color: var(--color-success-500);
  color: var(--color-success-700);
}

.alert-professional-warning {
  background: var(--color-warning-50);
  border-left-color: var(--color-warning-500);
  color: var(--color-warning-700);
}

.alert-professional-error {
  background: var(--color-error-50);
  border-left-color: var(--color-error-500);
  color: var(--color-error-700);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --color-neutral-0: #101828;
    --color-neutral-25: #1d2939;
    --color-neutral-50: #344054;
    --color-neutral-100: #475467;
    --color-neutral-200: #667085;
    --color-neutral-300: #98a2b3;
    --color-neutral-400: #d0d5dd;
    --color-neutral-500: #eaecf0;
    --color-neutral-600: #f2f4f7;
    --color-neutral-700: #f9fafb;
    --color-neutral-800: #fcfcfd;
    --color-neutral-900: #ffffff;

    --glass-bg: rgba(16, 24, 40, 0.25);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
  }

  .card-professional {
    background: var(--color-neutral-25);
    border-color: var(--color-neutral-200);
  }

  .input-professional {
    background: var(--color-neutral-25);
    border-color: var(--color-neutral-200);
    color: var(--color-neutral-900);
  }

  .nav-professional {
    background: var(--color-neutral-25);
    border-bottom-color: var(--color-neutral-200);
  }

  .table-professional {
    background: var(--color-neutral-25);
    border-color: var(--color-neutral-200);
  }

  .table-professional th {
    background: var(--color-neutral-50);
  }
}
