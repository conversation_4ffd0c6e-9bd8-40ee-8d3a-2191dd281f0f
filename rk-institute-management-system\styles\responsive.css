/**
 * Mobile-First Responsive Design System
 * 
 * Enhanced responsive utilities and mobile optimizations for the
 * RK Institute Management System. Follows mobile-first principles
 * with progressive enhancement for larger screens.
 * 
 * Features:
 * - Mobile-first breakpoint system
 * - Touch-friendly interactions
 * - Improved accessibility
 * - Safe area support for iOS
 * - Optimized spacing and typography
 */

/* Mobile-First Breakpoints */
:root {
  --breakpoint-sm: 640px;   /* Small tablets */
  --breakpoint-md: 768px;   /* Tablets */
  --breakpoint-lg: 1024px;  /* Small laptops */
  --breakpoint-xl: 1280px;  /* Desktops */
  --breakpoint-2xl: 1536px; /* Large desktops */
}

/* Safe Area Support for iOS */
@supports (padding: max(0px)) {
  .safe-area-inset-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }
  
  .safe-area-inset-bottom {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
  
  .safe-area-inset-left {
    padding-left: max(1rem, env(safe-area-inset-left));
  }
  
  .safe-area-inset-right {
    padding-right: max(1rem, env(safe-area-inset-right));
  }
}

/* Touch-Friendly Interactions */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  touch-action: manipulation;
}

.touch-target-large {
  min-height: 48px;
  min-width: 48px;
  touch-action: manipulation;
}

/* Mobile-Optimized Spacing */
.mobile-padding {
  padding: 1rem;
}

@media (min-width: 640px) {
  .mobile-padding {
    padding: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .mobile-padding {
    padding: 2rem;
  }
}

/* Mobile-First Container Sizes */
.container-mobile {
  width: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 640px) {
  .container-mobile {
    max-width: 640px;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px) {
  .container-mobile {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container-mobile {
    max-width: 1024px;
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1280px) {
  .container-mobile {
    max-width: 1280px;
  }
}

/* Mobile-Optimized Typography */
.text-mobile-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-mobile-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-mobile-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

@media (min-width: 640px) {
  .text-mobile-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
  
  .text-mobile-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }
  
  .text-mobile-lg {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1024px) {
  .text-mobile-sm {
    font-size: 1rem;
    line-height: 1.5rem;
  }
  
  .text-mobile-base {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
  
  .text-mobile-lg {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

/* Mobile-First Grid System */
.grid-mobile {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

.grid-mobile-2 {
  display: grid;
  gap: 0.75rem;
  grid-template-columns: repeat(2, 1fr);
}

@media (min-width: 640px) {
  .grid-mobile {
    gap: 1.5rem;
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-mobile-2 {
    gap: 1rem;
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 768px) {
  .grid-mobile {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .grid-mobile-2 {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-mobile {
    gap: 2rem;
    grid-template-columns: repeat(4, 1fr);
  }
  
  .grid-mobile-2 {
    gap: 1.5rem;
    grid-template-columns: repeat(6, 1fr);
  }
}

/* Mobile-Optimized Cards */
.card-mobile {
  background: white;
  border-radius: 0.75rem;
  padding: 1rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease-in-out;
}

.card-mobile:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

@media (min-width: 640px) {
  .card-mobile {
    padding: 1.5rem;
    border-radius: 1rem;
  }
}

@media (min-width: 1024px) {
  .card-mobile {
    padding: 2rem;
    border-radius: 1.25rem;
  }
}

/* Mobile-Optimized Buttons */
.btn-mobile {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.25rem;
  border-radius: 0.75rem;
  min-height: 44px;
  touch-action: manipulation;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  border: none;
  text-decoration: none;
}

.btn-mobile:active {
  transform: scale(0.95);
}

.btn-mobile-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-mobile-primary:hover {
  background-color: #2563eb;
}

.btn-mobile-secondary {
  background-color: #f3f4f6;
  color: #374151;
}

.btn-mobile-secondary:hover {
  background-color: #e5e7eb;
}

@media (min-width: 640px) {
  .btn-mobile {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
    border-radius: 0.875rem;
    min-height: 48px;
  }
}

@media (min-width: 1024px) {
  .btn-mobile {
    padding: 1rem 2rem;
    font-size: 1.125rem;
    border-radius: 1rem;
    min-height: 52px;
  }
}

/* Mobile-Optimized Forms */
.form-mobile {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.input-mobile {
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  line-height: 1.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.75rem;
  background-color: white;
  min-height: 44px;
  touch-action: manipulation;
  transition: all 0.2s ease-in-out;
}

.input-mobile:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

@media (min-width: 640px) {
  .form-mobile {
    gap: 1.5rem;
  }
  
  .input-mobile {
    padding: 0.875rem 1.25rem;
    border-radius: 0.875rem;
    min-height: 48px;
  }
}

@media (min-width: 1024px) {
  .input-mobile {
    padding: 1rem 1.5rem;
    font-size: 1.125rem;
    border-radius: 1rem;
    min-height: 52px;
  }
}

/* Mobile Navigation */
.nav-mobile {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #e5e7eb;
  padding: 0.5rem;
  z-index: 50;
}

.nav-mobile-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  min-height: 44px;
  min-width: 44px;
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
  touch-action: manipulation;
  cursor: pointer;
  text-decoration: none;
  color: #6b7280;
}

.nav-mobile-item:hover,
.nav-mobile-item.active {
  background-color: #eff6ff;
  color: #3b82f6;
}

.nav-mobile-item .icon {
  width: 1.5rem;
  height: 1.5rem;
  margin-bottom: 0.25rem;
}

.nav-mobile-item .label {
  font-size: 0.75rem;
  font-weight: 500;
  text-align: center;
}

/* Mobile-Optimized Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out forwards;
}

.animate-slide-down {
  animation: slideDown 0.3s ease-out forwards;
}

.animate-fade-in-scale {
  animation: fadeInScale 0.2s ease-out forwards;
}

/* Mobile-Specific Utilities */
.hide-mobile {
  display: none;
}

@media (min-width: 640px) {
  .hide-mobile {
    display: block;
  }
}

.show-mobile {
  display: block;
}

@media (min-width: 640px) {
  .show-mobile {
    display: none;
  }
}

/* Improved Focus States for Mobile */
.focus-mobile:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
  border-radius: 0.375rem;
}

/* Mobile-Optimized Scrolling */
.scroll-mobile {
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scroll-mobile::-webkit-scrollbar {
  display: none;
}

/* Mobile-Optimized Tables */
.table-mobile {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.table-mobile th,
.table-mobile td {
  padding: 0.75rem 0.5rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.table-mobile th {
  font-weight: 600;
  color: #374151;
  background-color: #f9fafb;
}

@media (min-width: 640px) {
  .table-mobile {
    font-size: 1rem;
  }
  
  .table-mobile th,
  .table-mobile td {
    padding: 1rem 0.75rem;
  }
}

@media (min-width: 1024px) {
  .table-mobile th,
  .table-mobile td {
    padding: 1.25rem 1rem;
  }
}
