/**
 * RK Institute Management System - Enhanced Typography System
 * 
 * Modern typography system with improved readability, accessibility,
 * and visual hierarchy across all screen sizes.
 * 
 * Features:
 * - Optimized font stacks for better performance
 * - Improved contrast ratios for accessibility
 * - Responsive typography scaling
 * - Professional heading hierarchy
 * - Enhanced body text readability
 */

/* Import Google Fonts - Inter for UI, Poppins for headings */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap');

/* CSS Custom Properties for Typography */
:root {
  /* Font Families */
  --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-body: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /* Font Sizes - Mobile First */
  --text-xs: 0.75rem;     /* 12px */
  --text-sm: 0.875rem;    /* 14px */
  --text-base: 1rem;      /* 16px */
  --text-lg: 1.125rem;    /* 18px */
  --text-xl: 1.25rem;     /* 20px */
  --text-2xl: 1.5rem;     /* 24px */
  --text-3xl: 1.875rem;   /* 30px */
  --text-4xl: 2.25rem;    /* 36px */
  --text-5xl: 3rem;       /* 48px */

  /* Line Heights */
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  /* Letter Spacing */
  --tracking-tighter: -0.05em;
  --tracking-tight: -0.025em;
  --tracking-normal: 0em;
  --tracking-wide: 0.025em;
  --tracking-wider: 0.05em;

  /* Text Colors with Enhanced Contrast */
  --text-primary: #1a202c;      /* High contrast for headings */
  --text-secondary: #2d3748;    /* Medium contrast for body */
  --text-tertiary: #4a5568;     /* Lower contrast for captions */
  --text-muted: #718096;        /* Muted text */
  --text-inverse: #ffffff;      /* White text on dark backgrounds */
  --text-accent: #3182ce;       /* Accent color for links */
  --text-success: #38a169;      /* Success messages */
  --text-warning: #d69e2e;      /* Warning messages */
  --text-error: #e53e3e;        /* Error messages */
}

/* Responsive Font Size Scaling */
@media (min-width: 640px) {
  :root {
    --text-sm: 0.875rem;    /* 14px */
    --text-base: 1rem;      /* 16px */
    --text-lg: 1.125rem;    /* 18px */
    --text-xl: 1.25rem;     /* 20px */
    --text-2xl: 1.5rem;     /* 24px */
    --text-3xl: 2rem;       /* 32px */
    --text-4xl: 2.5rem;     /* 40px */
    --text-5xl: 3.5rem;     /* 56px */
  }
}

@media (min-width: 1024px) {
  :root {
    --text-3xl: 2.25rem;    /* 36px */
    --text-4xl: 3rem;       /* 48px */
    --text-5xl: 4rem;       /* 64px */
  }
}

/* Base Typography Styles */
html {
  font-family: var(--font-body);
  font-size: 16px;
  line-height: var(--leading-normal);
  color: var(--text-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  font-family: var(--font-body);
  font-weight: var(--font-weight-normal);
  color: var(--text-secondary);
  background-color: #f7fafc;
}

/* Enhanced Heading Hierarchy */
.heading-display {
  font-family: var(--font-heading);
  font-size: var(--text-5xl);
  font-weight: var(--font-weight-extrabold);
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-tighter);
  color: var(--text-primary);
  margin-bottom: 1.5rem;
}

.heading-1 {
  font-family: var(--font-heading);
  font-size: var(--text-4xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-tight);
  color: var(--text-primary);
  margin-bottom: 1.25rem;
}

.heading-2 {
  font-family: var(--font-heading);
  font-size: var(--text-3xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--leading-snug);
  letter-spacing: var(--tracking-tight);
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.heading-3 {
  font-family: var(--font-heading);
  font-size: var(--text-2xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--leading-snug);
  color: var(--text-primary);
  margin-bottom: 0.875rem;
}

.heading-4 {
  font-family: var(--font-heading);
  font-size: var(--text-xl);
  font-weight: var(--font-weight-medium);
  line-height: var(--leading-snug);
  color: var(--text-primary);
  margin-bottom: 0.75rem;
}

.heading-5 {
  font-family: var(--font-heading);
  font-size: var(--text-lg);
  font-weight: var(--font-weight-medium);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  margin-bottom: 0.625rem;
}

.heading-6 {
  font-family: var(--font-heading);
  font-size: var(--text-base);
  font-weight: var(--font-weight-semibold);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: var(--tracking-wide);
}

/* Enhanced Body Text Styles */
.text-body-large {
  font-size: var(--text-lg);
  line-height: var(--leading-relaxed);
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.text-body {
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.text-body-small {
  font-size: var(--text-sm);
  line-height: var(--leading-normal);
  color: var(--text-tertiary);
  margin-bottom: 0.75rem;
}

.text-caption {
  font-size: var(--text-xs);
  line-height: var(--leading-normal);
  color: var(--text-muted);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: var(--tracking-wider);
}

/* Specialized Text Styles */
.text-lead {
  font-size: var(--text-xl);
  line-height: var(--leading-relaxed);
  color: var(--text-secondary);
  font-weight: var(--font-weight-normal);
  margin-bottom: 1.5rem;
}

.text-quote {
  font-size: var(--text-lg);
  line-height: var(--leading-relaxed);
  color: var(--text-tertiary);
  font-style: italic;
  border-left: 4px solid #e2e8f0;
  padding-left: 1rem;
  margin: 1.5rem 0;
}

.text-code {
  font-family: var(--font-mono);
  font-size: 0.875em;
  background-color: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  padding: 0.125rem 0.375rem;
  color: var(--text-primary);
}

/* Link Styles */
.link {
  color: var(--text-accent);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: color 0.2s ease-in-out;
}

.link:hover {
  color: #2c5aa0;
  text-decoration: underline;
}

.link:focus {
  outline: 2px solid var(--text-accent);
  outline-offset: 2px;
  border-radius: 0.125rem;
}

/* Status Text Colors */
.text-success {
  color: var(--text-success);
  font-weight: var(--font-weight-medium);
}

.text-warning {
  color: var(--text-warning);
  font-weight: var(--font-weight-medium);
}

.text-error {
  color: var(--text-error);
  font-weight: var(--font-weight-medium);
}

/* Utility Classes */
.font-heading {
  font-family: var(--font-heading);
}

.font-body {
  font-family: var(--font-body);
}

.font-mono {
  font-family: var(--font-mono);
}

.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}

/* Responsive Typography Utilities */
@media (max-width: 639px) {
  .mobile-text-sm .heading-1 {
    font-size: var(--text-3xl);
  }
  
  .mobile-text-sm .heading-2 {
    font-size: var(--text-2xl);
  }
  
  .mobile-text-sm .heading-3 {
    font-size: var(--text-xl);
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: #f7fafc;
    --text-secondary: #e2e8f0;
    --text-tertiary: #cbd5e0;
    --text-muted: #a0aec0;
    --text-accent: #63b3ed;
  }
  
  body {
    background-color: #1a202c;
  }
}
